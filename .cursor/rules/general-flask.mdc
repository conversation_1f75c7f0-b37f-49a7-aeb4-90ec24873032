---
description: 
globs: 
alwaysApply: true
---
---
description: Enforce Flask best practices with PostgreSQL, Alembic, caching, AdminLTE, and PEP8 compliance; prevent AI from auto-generating migrations
globs: app/**/*.py, migrations/**/*.py, templates/**/*.html, static/**/*, *.py
alwaysApply: true
---

- **Project Structure & Blueprints**
  - Use `Blueprint`s to modularize features like `auth`, `admin`, `routes`
  - Centralize registration in `create_app()` within `__init__.py`
  - Avoid route logic in the same file as app creation

- **Alembic Migration Rules**
  - ❌ **NEVER auto-generate or apply Alembic migrations**
  - ✅ **Only display a suggestion** like:
    ```bash
    echo "Suggested: alembic revision --autogenerate -m 'add foo column'"
    ```
  - Do not write files in `alembic/versions/`
  - Do not call `alembic upgrade` or `alembic revision` directly in any script or function

- **Caching (Flask-Caching)**
  - Use `@cache.cached(timeout=..., key_prefix='...')` for reusable endpoints
  - Don’t cache permissioned or user-specific views unless explicitly handled
  - Example:
    ```python
    @cache.cached(timeout=900, key_prefix='report-auth-status')
    def get_auth_status():
        ...
    ```

- **Database Use**
  - Use SQLAlchemy models (`db.Model`) for core logic
  - Use context-managed raw connections (`with safe_db_connection(...)`) for legacy DBs
  - Avoid raw SQL unless wrapped in `text()` with parameters
  - ❌ **Never use string formatting or f-strings in SQL**:
    ```python
    # ✅
    db.session.execute(text("SELECT * FROM users WHERE id = :id"), {'id': user_id})

    # ❌
    db.session.execute(f"SELECT * FROM users WHERE id = {user_id}")
    ```

- **RBAC & Permissions**
  - Always enforce both `@login_required` and `@check_permission('perm')` decorators
  - Use `has_permission()` logic on `User` and `Role` objects
  - Avoid hardcoded permission logic in templates or frontend JS

- **Templating (AdminLTE + Jinja2)**
  - All templates must extend `layouts/base.html`
  - Use `{% block %}` consistently and never duplicate `<head>` logic
  - Static files must use `url_for('static', filename='...')`
  - Use reusable components (`includes/`, `macros.html`) to avoid repetition

- **Logging & Health**
  - Use `logging.getLogger(__name__)` and `logger.info()` instead of `print()`
  - `/health` and `/version` routes must return valid JSON with version & DB status
  - Log startup via `=== APPLICATION STARTUP COMPLETE ===`

- **Configuration**
  - Use a `Config` class with environment variables
  - Never hardcode secrets; load with `os.getenv()`
  - Example:
    ```python
    class Config:
        SQLALCHEMY_DATABASE_URI = os.getenv("DATABASE_URL")
        SECRET_KEY = os.getenv("SECRET_KEY")
    ```

- **Front-End Design**
  - Always use AdminLTE for UI components and layouts
  - Reference official docs: https://adminlte.io/docs/3.2/

- **PEP8 & Python Best Practices**
  - ✅ Top-level imports only; no mid-function imports
  - ✅ Use 4 spaces per indent level, never tabs
  - ✅ Max line length: 88 characters (Black default)
  - ✅ Class names use `CamelCase`, functions and variables use `snake_case`
  - ✅ One class per file unless logically grouped
  - ✅ Remove unused variables and imports before committing
  - ✅ Use `is not None` instead of `!= None`
  - ❌ Don’t ignore exceptions silently — always log or handle them explicitly
  - ✅ Use `if __name__ == "__main__":` for CLI scripts
  - ✅ Type hints are recommended for all public functions
    ```python
    def get_user(user_id: int) -> User:
        ...
    ```

- **Best Practices Summary**
  ```python
  # ✅ DO
  from flask_caching import Cache
  from sqlalchemy import text

  @cache.cached(timeout=60, key_prefix='dashboard')
  def dashboard():
      ...

  db.session.execute(text("SELECT * FROM users WHERE id = :id"), {'id': user_id'})

  {% extends "layouts/base.html" %}
  {% block content %} ... {% endblock %}

  # ❌ DON'T
  alembic revision --autogenerate
  db.session.execute(f"SELECT * FROM users WHERE id = {user_id}")
  {% include "full_layout.html" %}
  print("debug")
