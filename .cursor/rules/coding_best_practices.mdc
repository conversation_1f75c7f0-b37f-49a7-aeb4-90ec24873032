---
description: Guidelines for general coding best practices.
globs: **/*
alwaysApply: true
---

# Rules

1. Make it a habit to commit lessons learned and next steps to your memory bank.

2. When beginning a task, be sure to brush up on the latest API docs using web search.

3. Avoid reinventing the wheel. There is almost always an open source solution or snipper you can find on github. Search github repos early and often, and use the search bar to find relevant code snippets or libraries that can help you achieve your goals.

4. Always codify your plan in the ./docs folder before beginning work on it. Never begin work on a task without getting explicit approval.. This will help you stay focused and ensure that you don't forget any important details.

5. Always codify your progress in your memory_bank as you work.

6. Never mark or assume a task is complete until either a test proves it or functionality is demonstrated and verified.

7. Always be thinking of where we should focus our attention next in order to maximize efficiency and lower friction. If no clear priority can be discerned, use your expert knowledge and experience as an accomplished infrastructure engineer to make an informed decision for me.

8. Always adhere to software engineering best practices and make liberal use of idiomatic patterns.

9. Favor composition over inheritance.

10. Always use abstraction to build a layered solution. Keep code DRY and modular. Patterns our repo will benefit from include: Command/Executor, Strategy, Template Method, Abstract Factory, Transactional Outbox, Flyweight, Chain of Command
