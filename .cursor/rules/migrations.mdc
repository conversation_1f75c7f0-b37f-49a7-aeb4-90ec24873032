---
description: 
globs: 
alwaysApply: true
---
---
description: Never create or apply Alembic migrations automatically; only display the suggested `flask db migrate` command
globs: alembic/versions/*.py, models/*.py, **/models.py
alwaysApply: true
---

- **Never Create or Apply Alembic Migrations Automatically**
  - Do **not** run `flask db migrate`, `flask db upgrade`, or direct Alembic commands in scripts or automation.
  - Do **not** auto-generate migration files in `alembic/versions/`.
  - Do **not** run migrations or apply schema changes as part of automated tasks.

- **Only Display the Suggested Migration Command**
  - If model changes are detected, output the recommended command for manual use.
  - Developers should review, run, and commit migrations themselves.

- **Example of Allowed Behavior**
  ```bash
  # ✅ DO: Output this as a suggestion only
  echo "Suggested: flask db migrate -m 'enhance message functionality'"
