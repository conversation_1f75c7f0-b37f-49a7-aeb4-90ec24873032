# Template Structure for Trinote 2.0

```
app/templates/
├── base.html                # Base template with common structure
├── components/              # Reusable UI components
│   ├── forms/               # Form components
│   ├── modals/              # Modal dialogs
│   └── navigation/          # Navigation elements
├── layouts/                 # Page layout templates
│   ├── admin.html           # Admin layout
│   └── dashboard.html       # Dashboard layout
└── pages/                   # Page-specific templates
    ├── admin/               # Admin pages
    ├── auth/                # Authentication pages
    └── dashboard/           # Dashboard pages
```

## Template Inheritance Pattern

All page templates should inherit from a layout template:

```html
{% extends "layouts/dashboard.html" %}

{% block title %}Page Title{% endblock %}

{% block content %}
  <!-- Page-specific content here -->
{% endblock %}
```

## Component Usage Pattern

Reusable components should be included using the include directive:

```html
{% include "components/forms/user_form.html" %}
```

## Permission-Based UI Elements

UI elements that require specific permissions should be conditionally rendered:

```html
{% if permissions.get('view_reports') %}
  <a href="{{ url_for('routes.reports') }}" class="btn btn-primary">View Reports</a>
{% endif %}
```

## Form Structure

Forms should follow this structure:

```html
<form method="POST" action="{{ url_for('routes.endpoint') }}">
  {{ form.csrf_token }}
  
  <div class="form-group">
    {{ form.field.label }}
    {{ form.field(class="form-control") }}
    {% if form.field.errors %}
      <div class="text-danger">
        {% for error in form.field.errors %}
          {{ error }}
        {% endfor %}
      </div>
    {% endif %}
  </div>
  
  <button type="submit" class="btn btn-primary">Submit</button>
</form>
```

## Flash Messages

Flash messages should be displayed in a consistent manner:

```html
{% with messages = get_flashed_messages(with_categories=true) %}
  {% if messages %}
    {% for category, message in messages %}
      <div class="alert alert-{{ category }}">
        {{ message }}
      </div>
    {% endfor %}
  {% endif %}
{% endwith %}
```
