{"python.linting.enabled": true, "python.linting.flake8Enabled": true, "python.linting.pylintEnabled": true, "python.formatting.provider": "black", "python.formatting.blackArgs": ["--line-length=100", "--skip-string-normalization"], "python.linting.flake8Args": ["--max-line-length=100", "--ignore=E203,W503"], "python.linting.pylintArgs": ["--disable=C0111,C0103,R0903,R0913", "--max-line-length=100"], "editor.formatOnSave": true, "editor.rulers": [100], "editor.tabSize": 4, "editor.insertSpaces": true, "files.exclude": {"**/__pycache__": true, "**/.pytest_cache": true, "**/*.pyc": true, ".venv": false}, "files.associations": {"*.html": "jinja-html"}, "emmet.includeLanguages": {"jinja-html": "html"}, "python.testing.pytestEnabled": true, "python.testing.unittestEnabled": false, "python.testing.nosetestsEnabled": false, "python.testing.pytestArgs": ["tests"], "[python]": {"editor.codeActionsOnSave": {"source.organizeImports": true}}, "sqltools.connections": [{"name": "Trinote PostgreSQL", "driver": "PostgreSQL", "server": "localhost", "port": 15432, "database": "db", "username": "trinote", "password": "admin123"}, {"name": "Trinote SQL Server", "driver": "MSSQL", "server": "localhost", "port": 1433, "database": "master", "username": "sa", "password": "Admin123!"}]}