# Setting Up the Project Profile

This document explains how to integrate the project profile with your development workflow.

## IDE Integration

### VS Code

1. Copy the VS Code settings to your workspace settings:

```bash
cp .project-profile/vscode-settings.json .vscode/settings.json
```

2. Install recommended extensions:
   - Python
   - Pylance
   - Flake8
   - Black Formatter
   - SQLTools
   - Jinja

### PyCharm

1. Import code style settings:
   - Go to Settings > Editor > Code Style > Python
   - Import scheme from `.project-profile/linting-config.json`

2. Configure inspections based on the linting rules

## Git Hooks

Set up pre-commit hooks to enforce code style:

1. Install pre-commit:

```bash
pip install pre-commit
```

2. Create a `.pre-commit-config.yaml` file:

```yaml
repos:
-   repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.4.0
    hooks:
    -   id: trailing-whitespace
    -   id: end-of-file-fixer
    -   id: check-yaml
    -   id: check-added-large-files

-   repo: https://github.com/pycqa/isort
    rev: 5.12.0
    hooks:
    -   id: isort
        args: ["--profile", "black", "--line-length", "100"]

-   repo: https://github.com/psf/black
    rev: 23.3.0
    hooks:
    -   id: black
        args: ["--line-length", "100", "--skip-string-normalization"]

-   repo: https://github.com/pycqa/flake8
    rev: 6.0.0
    hooks:
    -   id: flake8
        args: ["--max-line-length", "100", "--ignore", "E203,W503"]
```

3. Install the hooks:

```bash
pre-commit install
```

## Documentation Integration

Add a reference to the project profile in the main README.md:

```markdown
## Development Guidelines

This project follows specific coding standards and workflows defined in the `.project-profile` directory.
New contributors should review these guidelines before submitting code.

See [Project Profile README](.project-profile/README.md) for more information.
```

## Team Onboarding

When onboarding new team members:

1. Include a review of the project profile in the onboarding process
2. Walk through the code patterns and workflow guides
3. Ensure their development environment is configured according to the profile

## Continuous Integration

Update CI configuration to use the same linting rules:

1. Add linting checks to the CI pipeline
2. Use the same configuration as defined in `.project-profile/linting-config.json`
3. Make passing linting checks a requirement for merging pull requests
