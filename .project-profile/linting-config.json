{"flake8": {"max-line-length": 100, "exclude": [".git", "__pycache__", ".venv", "migrations", "vendor"], "ignore": ["E203", "W503"]}, "pylint": {"disable": ["C0111", "C0103", "R0903", "R0913"], "max-line-length": 100, "good-names": ["db", "id", "e", "bp"]}, "black": {"line-length": 100, "skip-string-normalization": true, "exclude": "/(migrations|vendor)/"}, "isort": {"line_length": 100, "multi_line_output": 3, "include_trailing_comma": true, "force_grid_wrap": 0, "use_parentheses": true, "ensure_newline_before_comments": true, "skip": ["migrations", "vendor"]}}