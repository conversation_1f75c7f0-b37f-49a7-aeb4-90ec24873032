# Trinote 2.0 Project Profile

This directory contains configuration files, guidelines, and reference patterns for the Trinote 2.0 project. These resources are designed to help maintain consistency and follow established patterns when developing features for the application.

## Contents

- **project-structure.json**: Defines the overall project structure and key directories
- **code-style.json**: Coding style guidelines for the project
- **database-conventions.json**: Database naming conventions and ORM usage
- **auth-rules.json**: Authentication and authorization patterns
- **code-patterns.py**: Reference implementations of common code patterns
- **template-structure.md**: Template organization and usage patterns
- **workflow-guides.md**: Development, testing, and deployment workflows
- **deployment-checklist.md**: Pre-deployment and deployment verification steps
- **linting-config.json**: Configuration for code linting tools

## How to Use This Profile

### For New Developers

1. Read through the documentation in this directory to understand the project structure and conventions
2. Reference the code patterns when implementing new features
3. Follow the workflow guides for development tasks
4. Use the deployment checklist when preparing releases

### For Code Reviews

1. Use these guidelines as a reference when reviewing pull requests
2. Ensure new code follows the established patterns
3. Check that appropriate tests are included
4. Verify that documentation is updated as needed

### For IDE Configuration

The linting configuration can be used to set up your IDE:

- **VS Code**: Copy settings to `.vscode/settings.json`
- **PyCharm**: Import settings from the linting configuration
- **Other IDEs**: Configure linting tools according to the specifications

## Keeping the Profile Updated

As the project evolves, this profile should be updated to reflect new patterns and best practices:

1. When introducing a significant new pattern, add it to the appropriate file
2. Update workflow guides when processes change
3. Keep the deployment checklist current with new requirements
4. Review and update the profile quarterly to ensure it remains relevant
