# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

Trinote 2.0 is a modern task management system for ABA providers, built with:

- **Backend**: Python/Flask with dual database support (PostgreSQL + MSSQL)
- **Frontend**: AdminLTE with React/shadcn components mounted into content areas
- **Infrastructure**: Docker for local dev, DigitalOcean Droplet for production
- **Production**: 7,688+ users, 2,535+ students at ************* (triumph_dev_db)
- **Current State**: Phone log feature 64% complete (7/11 tasks), focusing on parent communication

## Commands

### Development Environment

```bash
# Switch environments (local vs production data)
./scripts/switch-env.sh local      # Use local databases (default)
./scripts/switch-env.sh prod-data  # Use production MSSQL data

# Start Docker environment with initialized databases
mise run start_docker

# Watch logs
mise run watch_dev

# Initialize MSSQL database (if needed separately)
mise run init-mssql

# React dashboard development
mise run build_dashboard_react    # Build components
mise run dev_dashboard_react      # Start dev server
```

### Authentication & Login

```bash
# Application runs at http://localhost:5001
# Authentication is required - login page redirects automatically

# Development Login Options:
# 1. Admin user (full access):
#    Username: admin
#    Password: admin

# 2. Production user (Hanah Hayum - therapist with 24 students):
#    Username: gittyhayum (or <EMAIL>)
#    Password: any (any password works in dev mode)

# Available Routes:
# / - Redirects to dashboard if authenticated, login if not
# /login - Login page
# /logout - Logout and return to login
# /dashboard - Main dashboard (requires authentication)
# /auth/test-auth - Authentication status check
```

### Database Access

```bash
# PostgreSQL (port 15432 to avoid conflicts)
docker exec -it trinote20-postgres_db-1 psql -U trinote db

# MSSQL
docker exec -it trinote20-mssql_db-1 /opt/mssql-tools18/bin/sqlcmd -S localhost -U sa -P Admin123! -C
```

### Testing & Validation

```bash
# Run tests
pytest

# JWT authentication test
integration_tests/authentication/test_jwt_api.sh

# Guided form API test
integration_tests/authentication/test_guided_form_api.sh

# Lint and typecheck (when available)
# Check README or search codebase for specific commands
```

### Test Data Generation

```bash
# Reset seed data (5 test therapists with varying caseloads)
mise run reset-seed-data

# Clean up test data (all prefixed with TEST_SEED_)
mise run cleanup-seed-data

# Configuration: scripts/seed_data/dashboard_seed_config.yaml
# Scenarios: Heavy Load (30 students), Moderate, Light, No Load, Mixed
```

### Task Master Workflow

```bash
# Daily workflow
task-master next                              # Find next task
task-master show <id>                        # View task details
task-master set-status --id=<id> --status=in-progress
# ... implement task ...
task-master set-status --id=<id> --status=done

# Task management
task-master list --status=pending            # View pending tasks
task-master expand <id>                      # Create subtasks
task-master update-task --id=<id> --prompt="..." # Update with new info

# Project location: .taskmaster/tasks/tasks.json
```

## Architecture

### Backend Structure

- `run.py` - Application entry point
- `config.py` - Configuration management with Vault integration
- `app/` - Main application code
  - `models.py` - SQLAlchemy models
  - `routes.py` - Flask routes
  - `auth.py` - Authentication logic
  - `middleware/` - Auth and permission middleware
  - `utils/` - Encryption, RBAC utilities

### Frontend Structure

- `app/frontend/dashboard/` - React dashboard components with shadcn/ui
- `app/frontend/react_root_components/` - Additional React components
  - Vite-based build system
  - Tailwind CSS for styling
  - Components mount into AdminLTE content areas
  - Current: `SimplePhoneLogPage.tsx` for phone log feature

### Database Strategy

- **PostgreSQL**: Primary database for application data
- **MSSQL**: Legacy database integration (configurable)
- Migrations handled via Flask-Migrate for PostgreSQL
- MSSQL initialization via scripts in `sql/`

#### Environment Configuration

- **Local Environment**: Uses local Docker containers for both databases
- **Production Data Environment**: Uses production MSSQL + local PostgreSQL
- Environment files: `.env.local`, `.env.prod-data`
- Switch with: `./scripts/switch-env.sh [local|prod-data]`

### Permission System

- **Three-layer enforcement**: decorator-based, route-based, code-level
- **Naming convention**: `entity.action` (e.g., `user.create`, `report.view`)
- **Management interfaces**: `/admin/permissions`, `/admin/routes`, `/admin/roles`
- **Critical permissions**: Cannot be deleted
- **Import/export**: Available for route permissions

### API Response Format

```python
# Standard response structure
{
    "status": "success|error",
    "data": {...} or null,
    "message": "Optional message",
    "error": "Error details if status=error"
}

# Session variables
session['user_id'], session['username'], session['role_id']
session['office_id'], session['is_case_manager']
```

## Key Development Practices

### Docker Compose

- Uses `docker compose` not `docker-compose`
- Health checks ensure proper service startup order
- Dev environment includes auto-initialization

### Task Management

- **Task Master AI**: Primary project management (use `task-master-ai` MCP tools)
- **mise**: Task automation with domain modules (env, db, dev, dashboard, cicd, tests)
- **Shell hook**: Runs on directory entry (`scripts/mise/shell_start_hook.sh`)
- **Tasks location**: `.taskmaster/tasks/tasks.json`
- **Current focus**: Phone log feature implementation (Tasks 5.1, 8-10)

### Code Conventions

- Follow existing patterns in codebase
- React components use shadcn/ui design system
- New features prefer React over legacy AdminLTE
- Database credentials managed via Vault in production

### Security

- Vault integration for secrets management
- Development uses hardcoded credentials (see docker-compose.yml)
- Production credentials stored in Vault at `secret/trinote/{env}/db`

## Important Notes

- **mise.toml location**: Project uses `mise.toml` in project root
- **Port conflicts**: PostgreSQL runs on port 15432 (not 5432) to avoid local conflicts
- **Admin bypass**: Set `BYPASS_AUTH_FOR_ADMIN=true` for dev login (admin/admin)
- **React components**: Two locations - `app/frontend/dashboard/` and `app/frontend/react_root_components/`
- **API Documentation**: Complete endpoint documentation in `docs/api-endpoints.md`
- **Parent communication**: Auth temporarily disabled for development endpoints
- **Production DB access**: Available via mise tasks (dump/import capabilities)

## Environment Management

### Database Environments

The application supports two database configurations:

1. **Local Development** (default)

   - Local MSSQL Docker container
   - Local PostgreSQL Docker container
   - Mock/seed data for development

2. **Production Data Testing**
   - Production MSSQL database (real data)
   - Local PostgreSQL container
   - 7,688+ real users and 2,535+ students

### Switching Environments

```bash
# Switch to local development (default)
./scripts/switch-env.sh local
docker compose --profile local up -d

# Switch to production data testing
./scripts/switch-env.sh prod-data
docker compose up -d

# Check current environment status
cat .env
```

### Environment Files

- `.env.local` - Local database configuration
- `.env.prod-data` - Production MSSQL configuration
- `.env` - Active environment (copied from above)

## Project-Specific Rules

### Task Management

- Use `task-master-ai` MCP tools for project planning
- Tasks must have measurable acceptance criteria
- Never mark tasks complete without validation

### Documentation

- Place docs in `@docs/`
- Place scripts in `@scripts/`
- Follow Common Project Framework (see `@docs/cpf.md`)
- Session directory: `@docs/session/`

### Memory Bank

- Custom folder: `@docs/memory_bank`
- Keep updated after each action
- Never attempt to initialize a new memory bank

### Architecture Guidelines

See `@docs/rules/northstar.md` for north star principles

### General Coding Rules

See `@docs/rules/GENERAL_CODING.md` for detailed practices

### Persona

See `@docs/rules/bestfriend.md` for interaction style

## Specific Development Memories

- Don't assume docker as the default for local development. Use native `python run.py`
- Use `mise` for task management
- Prefer pnpm over npm for package management

## Flask Development Best Practices

### Database & Migrations

- **NEVER auto-generate or apply Alembic migrations** - only suggest commands:
  ```bash
  echo "Suggested: alembic revision --autogenerate -m 'add foo column'"
  ```
- Use SQLAlchemy models (`db.Model`) for core logic
- Use context-managed connections for legacy databases
- **Never use f-strings or string formatting in SQL** - always use parameterized queries:
  ```python
  # ✅ Correct
  db.session.execute(text("SELECT * FROM users WHERE id = :id"), {'id': user_id})
  
  # ❌ Wrong
  db.session.execute(f"SELECT * FROM users WHERE id = {user_id}")
  ```

### Project Structure

- Use Flask Blueprints to modularize features (`auth`, `admin`, `routes`)
- Centralize Blueprint registration in `create_app()` within `__init__.py`
- Keep route logic separate from app creation

### Caching

- Use `@cache.cached(timeout=..., key_prefix='...')` for reusable endpoints
- Don't cache user-specific or permissioned views unless explicitly handled
- Example:
  ```python
  @cache.cached(timeout=900, key_prefix='report-auth-status')
  def get_auth_status():
      ...
  ```

### Security & Permissions

- Always enforce both `@login_required` and `@check_permission('perm')` decorators
- Use `has_permission()` logic on `User` and `Role` objects
- Avoid hardcoded permission logic in templates or frontend JS

### Frontend & Templating

- All templates must extend `layouts/base.html`
- Use `{% block %}` consistently, never duplicate `<head>` logic
- Static files must use `url_for('static', filename='...')`
- Use reusable components (`includes/`, `macros.html`) to avoid repetition
- Always use AdminLTE for UI components and layouts

### Logging & Health

- Use `logging.getLogger(__name__)` and `logger.info()` instead of `print()`
- `/health` and `/version` routes must return valid JSON with version & DB status
- Log startup via `=== APPLICATION STARTUP COMPLETE ===`

### Python Code Style (PEP8)

- Top-level imports only; no mid-function imports
- Use 4 spaces per indent level, never tabs
- Max line length: 88 characters (Black default)
- Class names use `CamelCase`, functions and variables use `snake_case`
- One class per file unless logically grouped
- Remove unused variables and imports before committing
- Use `is not None` instead of `!= None`
- Don't ignore exceptions silently — always log or handle them explicitly
- Use `if __name__ == "__main__":` for CLI scripts
- Type hints are recommended for all public functions:
  ```python
  def get_user(user_id: int) -> User:
      ...
  ```

## Software Architecture Principles

### North Star Principles

1. **User-Centric Development**
   - Every task must add intrinsic value to users
   - Focus on features that directly impact outcomes
   - Design interfaces and reports for clarity and actionability

2. **Results-Focused Implementation**
   - No task is complete without validation through testing
   - All acceptance criteria must be measurable and testable
   - Implementation must demonstrate real, actual results
   - Continuous validation throughout development lifecycle

3. **Walking Skeleton Approach**
   - Implement wide-and-shallow for complex features
   - Establish complete data flow before deep implementation
   - Create minimal working implementations for all components
   - Focus on system integration before detailed functionality

4. **Layer-First Design**
   - Strict separation of concerns
   - Single Responsibility Principle at method level
   - Promote modularity through clear layer boundaries
   - Design for reusability through abstraction

5. **Composition Over Inheritance**
   - Favor object composition over class inheritance
   - Build complex behaviors from simple components
   - Use dependency injection for flexibility
   - Design for plug-and-play component replacement

6. **Strong Encapsulation**
   - Package parameters in domain-specific payloads
   - Return results in structured response objects
   - Encode usage patterns in the type system
   - Maintain clear boundaries between components

### Design Patterns

Preferred patterns for this codebase:
- Command/Executor
- Strategy
- Template Method
- Abstract Factory
- Transactional Outbox
- Flyweight
- Chain of Command

### General Development Practices

1. **Planning & Documentation**
   - Codify plans in `./docs` before beginning work
   - Never begin work without explicit approval
   - Update memory bank/knowledge graph as you work

2. **Research & Reuse**
   - Check latest API docs before starting tasks
   - Search GitHub for existing solutions before implementing
   - Avoid reinventing the wheel

3. **Testing & Validation**
   - Never mark tasks complete without tests or demonstrated functionality
   - All features must have measurable acceptance criteria
   - Continuous validation throughout development

4. **Efficiency & Focus**
   - Always think about next focus areas to maximize efficiency
   - Use expert knowledge to make informed decisions when priorities unclear
   - Keep code DRY and modular

### Cursor Rules Structure

When creating Cursor rules (`.cursor/rules/*.mdc`):
- Use required frontmatter: `description`, `globs`, `alwaysApply`
- Reference files with `[filename](mdc:path/to/file)` format
- Include both DO and DON'T examples
- Use language-specific code blocks
- Keep rules DRY by referencing other rules

## Phone Log Module Implementation

### Database Schema

```sql
-- PhoneLogs table
CREATE TABLE PhoneLogs (
    id SERIAL PRIMARY KEY,
    client_id INTEGER NOT NULL,
    call_date DATE NOT NULL,
    call_time TIME,
    duration INTEGER,
    call_type_id INTEGER,
    notes TEXT,
    completed BOOLEAN DEFAULT FALSE,
    -- ... other fields
);

-- CallTypes lookup table
CREATE TABLE CallTypes (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL
);
```

### API Endpoints

```python
# Create phone log
POST /api/v1/clients/<int:client_id>/phone_logs

# Get metadata and form options
GET /api/v1/clients/<int:client_id>/phone_log_meta

# Flask route for UI
GET /clients/<int:client_id>/log_call
```

### Component Structure

- `PhoneLogPage.tsx` - Main page component
- `SimplePhoneLogPage.tsx` - Simplified version (current)
- Sub-components for form fields and communication table
- Integration with dashboard "Parent Communication Manager"

### Branching Strategy

- Main feature branch: `feature/phonelog`
- Sub-features: `feature/phonelog/[specific-feature]`
- PRs: sub-feature → `feature/phonelog` → `main`

## Quick Reference

### Common Workflows

```bash
# Start development
mise run start_docker          # Start all services
mise run watch_dev            # Watch logs
python run.py                 # Native development (no Docker)

# Frontend development
cd app/frontend/dashboard
pnpm install                  # Install dependencies
mise run dev_dashboard_react  # Start dev server

# Database operations
mise run db:dump-prod         # Dump production database
mise run db:import-prod       # Import production dump
mise run reset-seed-data      # Reset test data

# Task management
task-master next              # Get next task
task-master show <id>         # View task details
task-master list --status=pending

# Testing
pytest                        # Run all tests
pytest -k test_name           # Run specific test
```

### Key Files & Locations

- Tasks: `.taskmaster/tasks/tasks.json`
- API docs: `docs/backend/api-endpoints.md`
- Memory bank: `docs/memory_bank/`
- Cursor rules: `.cursor/rules/`
- Test data config: `scripts/seed_data/dashboard_seed_config.yaml`
- Environment switching: `./scripts/switch-env.sh`

### Current Priorities

1. Phone log feature completion (Tasks 5.1, 8-10)
2. Parent communication table integration
3. Dynamic prompts for phone calls
4. Form functionality restoration
