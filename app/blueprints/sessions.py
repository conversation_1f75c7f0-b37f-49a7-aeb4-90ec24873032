import csv
import io
import logging
from datetime import datetime, timedelta
from contextlib import contextmanager
from math import ceil
from sqlalchemy.exc import DatabaseError, SQLAlchemyError

from flask import Blueprint, render_template, request, redirect, url_for, flash, Response, current_app, session

from app import cache
from app.auth import login_required
from app.database import get_read_only_connection, get_write_connection
from app.middleware.permission_check import check_permission
from app import make_cache_key
from app.utils.encryption import decrypt_data

# Get logger for this module
logger = logging.getLogger(__name__)

# Create the blueprint with a url_prefix
sessions_bp = Blueprint('sessions', __name__, url_prefix='/sessions')

# Cache version for cache invalidation
CACHE_VERSION = 1

# Database Index Recommendations for Performance Optimization:
# 
# For vwSessions view (or underlying tables):
# 1. INDEX on (Sessdate, therapistID) - for date range queries with provider filter
# 2. INDEX on (Status_Name, BillingStatusName) - for status filtering
# 3. INDEX on (therapistID, Sessdate DESC) - for provider-specific date sorting
# 4. INDEX on (sessID) - should already exist as PK
# 5. INDEX on (CodeID, Sessdate) - for code filtering with dates
# 6. COVERING INDEX on (Sessdate DESC, starttime DESC) INCLUDE (all SELECT columns)
#    - This would make the main query much faster by avoiding key lookups
# 
# For vwStaff view:
# 7. INDEX on (LoginID) - for JOIN performance
# 8. INDEX on (FullName) - for provider name search
#
# Note: Views cannot have indexes directly - these should be applied to underlying tables

# Use centralized database connection logic
@contextmanager
def get_db_connection(write_access=False, timeout=10, operation_name="database operation"):
    """
    Get appropriate database connection based on access type.
    Uses get_write_connection for write operations and get_read_only_connection for read operations.
    
    Args:
        write_access (bool): Whether write access is needed
        timeout (int): Query timeout in seconds
        operation_name (str): Name of the operation for logging purposes
        
    Yields:
        Connection: The database connection object
    """
    if write_access:
        # Use get_write_connection for write operations - it's already a context manager
        with get_write_connection() as conn:
            # Set timeout for queries (in seconds)
            conn.timeout = timeout
            logger.debug(f"Write database connection established with {timeout}s timeout for {operation_name}")
            yield conn
    else:
        # Use read-only connection for read operations
        conn = get_read_only_connection()
        try:
            # Set timeout for queries (in seconds)
            conn.timeout = timeout
            logger.debug(f"Read-only database connection established with {timeout}s timeout for {operation_name}")
            yield conn
        except Exception as e:
            logger.error(f"Database error during {operation_name}: {str(e)}")
            raise
        finally:
            if conn:
                try:
                    conn.close()
                    logger.debug(f"Database connection closed after {operation_name}")
                except Exception as close_error:
                    logger.error(f"Error closing database connection after {operation_name}: {str(close_error)}")


def _add_multi_filter(base_query, parameters, filter_values, column_name):
    """
    Add multi-value filter to query with IN clause.
    
    Args:
        base_query (str): Current query string
        parameters (list): Current parameters list
        filter_values (list): List of filter values (can be strings or integers)
        column_name (str): Database column name
        
    Returns:
        tuple: (updated_query, updated_parameters)
    """
    # Filter out empty values - handle both strings and integers
    valid_values = []
    for v in filter_values:
        if v is not None:
            if isinstance(v, str):
                if v.strip():  # Non-empty string after stripping
                    valid_values.append(v.strip())
            elif isinstance(v, (int, float)):
                valid_values.append(v)  # Numbers are always valid
    
    if valid_values:
        if len(valid_values) == 1:
            base_query += f" AND {column_name} = ?"
            parameters.append(valid_values[0])
        else:
            placeholders = ','.join('?' * len(valid_values))
            base_query += f" AND {column_name} IN ({placeholders})"
            parameters.extend(valid_values)
    
    return base_query, parameters


def _parse_session_ids(session_id_input):
    """
    Parse session ID input to handle multiple IDs from various sources like Excel.
    
    Args:
        session_id_input (str): Raw input that may contain single or multiple session IDs
        
    Returns:
        list: List of valid session IDs as integers
    """
    if not session_id_input or not session_id_input.strip():
        return []
    
    # Replace common separators with commas for uniform processing
    # Handle: newlines, tabs, semicolons, pipes, and multiple spaces
    normalized = session_id_input.replace('\n', ',').replace('\r', ',').replace('\t', ',')
    normalized = normalized.replace(';', ',').replace('|', ',').replace('  ', ' ')
    normalized = normalized.replace(' ', ',')
    
    # Split by commas and clean up
    raw_ids = [id_str.strip() for id_str in normalized.split(',')]
    
    # Filter out empty strings and convert to integers
    valid_ids = []
    for id_str in raw_ids:
        if id_str:  # Skip empty strings
            try:
                # Try to convert to integer
                session_id = int(id_str)
                if session_id > 0:  # Only positive integers
                    valid_ids.append(session_id)
            except ValueError:
                # Skip invalid entries
                logger.warning(f"Skipping invalid session ID: {id_str}")
                continue
    
    # Remove duplicates while preserving order
    seen = set()
    unique_ids = []
    for id_val in valid_ids:
        if id_val not in seen:
            seen.add(id_val)
            unique_ids.append(id_val)
    
    return unique_ids


def _build_sessions_query(filters):
    """
    Build optimized sessions query with proper parameterization.
    
    Args:
        filters (dict): Filter parameters
        
    Returns:
        tuple: (query_string, parameters_list)
    """
    base_query = """
        SELECT 
            vs.sessID as SessionID,
            vs.FullName as ClientName,
            staff.FullName as ProviderName,
            FORMAT(vs.Sessdate, 'MM/dd/yyyy') as Sessdate,
            DATENAME(WEEKDAY, vs.Sessdate) as DayOfWeek,
            CASE 
                WHEN vs.starttime IS NOT NULL 
                THEN 
                    LTRIM(RIGHT(CONVERT(VARCHAR, CAST('1900-01-01 ' + CAST(vs.starttime AS VARCHAR) AS DATETIME), 100), 7))
                ELSE NULL 
            END as starttime,
            CASE 
                WHEN vs.endtime IS NOT NULL 
                THEN 
                    LTRIM(RIGHT(CONVERT(VARCHAR, CAST('1900-01-01 ' + CAST(vs.endtime AS VARCHAR) AS DATETIME), 100), 7))
                ELSE NULL 
            END as endtime,
            lt.LocationType,
            vs.CodeNameAndDesc as CodeDescription,
            vs.Status_Name,
            vs.BillingStatusName,
            FORMAT(vs.DateSubmitted, 'MM/dd/yyyy') as DateSubmitted,
            CASE 
                WHEN vs.payrollstatus = 14 THEN 'Yes'
                ELSE 'No'
            END as PaidStatus,
            CASE 
                WHEN ca.CA_Name IS NULL OR ca.CA_Name = '0' THEN 'Triumph'
                ELSE ca.CA_Name 
            END as AccountName,
            vws.CaseManagerName
        FROM vwSessions vs
        LEFT JOIN vwStaff staff ON vs.therapistID = staff.LoginID
        LEFT JOIN LocationTypes lt ON vs.LocationID = lt.LocationTypeID
        LEFT JOIN vwStudents vws ON vs.studentID = vws.studentID
        LEFT JOIN tblCorporateAccounts ca ON vws.CorporateAccount = ca.CA_ID
        WHERE 1=1
    """
    
    parameters = []
    
    # Add date filters if provided
    if filters.get('from_date'):
        base_query += " AND vs.Sessdate >= ?"
        parameters.append(filters['from_date'])
        
    if filters.get('to_date'):
        base_query += " AND vs.Sessdate <= ?"
        parameters.append(filters['to_date'])
    
    # Add status filters using integer IDs (much more efficient)
    session_status_values = filters.get('session_status', [])
    if session_status_values:
        # Convert to integers and filter out invalid values
        valid_session_status_ids = []
        for status_id in session_status_values:
            try:
                if status_id and str(status_id).strip():
                    valid_session_status_ids.append(int(status_id))
            except (ValueError, TypeError):
                logger.warning(f"Invalid session status ID: {status_id}")
        
        if valid_session_status_ids:
            base_query, parameters = _add_multi_filter(base_query, parameters, valid_session_status_ids, 'vs.SessionStatus')
    
    billing_status_values = filters.get('billing_status', [])
    if billing_status_values:
        # Convert to integers and filter out invalid values
        valid_billing_status_ids = []
        for status_id in billing_status_values:
            try:
                if status_id and str(status_id).strip():
                    valid_billing_status_ids.append(int(status_id))
            except (ValueError, TypeError):
                logger.warning(f"Invalid billing status ID: {status_id}")
        
        if valid_billing_status_ids:
            base_query, parameters = _add_multi_filter(base_query, parameters, valid_billing_status_ids, 'vs.BillingStatus')
    
    # Add ID-based filters (much more efficient)
    client_values = filters.get('client', [])
    if client_values:
        # Convert to integers and filter out invalid values
        valid_client_ids = []
        for client_id in client_values:
            try:
                if client_id and str(client_id).strip():
                    valid_client_ids.append(int(client_id))
            except (ValueError, TypeError):
                logger.warning(f"Invalid client ID: {client_id}")
        
        if valid_client_ids:
            base_query, parameters = _add_multi_filter(base_query, parameters, valid_client_ids, 'vs.studentID')
    
    provider_values = filters.get('provider', [])
    if provider_values:
        # Convert to integers and filter out invalid values
        valid_provider_ids = []
        for provider_id in provider_values:
            try:
                if provider_id and str(provider_id).strip():
                    valid_provider_ids.append(int(provider_id))
            except (ValueError, TypeError):
                logger.warning(f"Invalid provider ID: {provider_id}")
        
        if valid_provider_ids:
            base_query, parameters = _add_multi_filter(base_query, parameters, valid_provider_ids, 'vs.therapistID')
        
    if filters.get('session_id'):
        # Parse session IDs - handle multiple IDs separated by various delimiters
        session_ids = _parse_session_ids(filters['session_id'])
        if session_ids:
            if len(session_ids) == 1:
                base_query += " AND vs.sessID = ?"
                parameters.append(session_ids[0])
            else:
                # Multiple session IDs - use IN clause
                placeholders = ','.join('?' * len(session_ids))
                base_query += f" AND vs.sessID IN ({placeholders})"
                parameters.extend(session_ids)
        
    # Add remaining multi-select filters
    base_query, parameters = _add_multi_filter(base_query, parameters, filters.get('code_id', []), 'vs.CodeID')
    
    # Handle account filtering with special logic for "Triumph" (CA_ID = 0)
    account_values = filters.get('account', [])
    if account_values:
        valid_account_ids = []
        has_triumph = False
        
        for account_id in account_values:
            try:
                if account_id.strip():
                    account_id_int = int(account_id)
                    if account_id_int == 0:
                        has_triumph = True
                    else:
                        valid_account_ids.append(account_id_int)
            except (ValueError, TypeError):
                logger.warning(f"Invalid account ID: {account_id}")
        
        # Build account filter condition
        account_conditions = []
        if valid_account_ids:
            if len(valid_account_ids) == 1:
                account_conditions.append("ca.CA_ID = ?")
                parameters.append(valid_account_ids[0])
            else:
                placeholders = ','.join('?' * len(valid_account_ids))
                account_conditions.append(f"ca.CA_ID IN ({placeholders})")
                parameters.extend(valid_account_ids)
        
        if has_triumph:
            # Include records where CA_ID is 0 OR CA_Name is NULL or '0' (Triumph logic)
            account_conditions.append("(ca.CA_ID = 0 OR ca.CA_Name IS NULL OR ca.CA_Name = '0')")
        
        if account_conditions:
            base_query += f" AND ({' OR '.join(account_conditions)})"
    
    base_query, parameters = _add_multi_filter(base_query, parameters, filters.get('case_manager', []), 'vws.CaseManager')
    
    return base_query, parameters


def _get_sessions_count(conn, base_query, parameters):
    """Get total count of sessions matching the criteria."""
    count_query = f"SELECT COUNT(*) as total_count FROM ({base_query}) as counted_sessions"
    
    try:
        # For raw connections, use execute with string query and parameters
        cursor = conn.cursor()
        
        if parameters:
            cursor.execute(count_query, parameters)
        else:
            cursor.execute(count_query)
        
        row = cursor.fetchone()
        cursor.close()
        return row[0] if row else 0
    except Exception as e:
        logger.error(f"Error getting sessions count: {str(e)}")
        logger.error(f"Query was: {count_query}")
        logger.error(f"Parameters were: {parameters}")
        return 0


def _get_autocomplete_data(conn):
    """Get unique client and provider names with IDs and additional info for enhanced dropdowns."""
    try:
        cursor = conn.cursor()
        
        # Get unique clients with their IDs, encrypted DOB, and Corporate Account
        cursor.execute("""
            SELECT DISTINCT vs.studentID, vs.FullName, s.DOB,
                   CASE 
                       WHEN ca.CA_Name IS NULL OR ca.CA_Name = '0' THEN 'Triumph'
                       ELSE ca.CA_Name 
                   END as CorporateAccount
            FROM vwSessions vs 
            LEFT JOIN student s ON vs.studentID = s.studentID
            LEFT JOIN vwStudents vws ON vs.studentID = vws.studentID
            LEFT JOIN tblCorporateAccounts ca ON vws.CorporateAccount = ca.CA_ID
            WHERE vs.FullName IS NOT NULL 
            ORDER BY vs.FullName
        """)
        client_data = []
        for row in cursor.fetchall():
            student_id, full_name, encrypted_dob, corporate_account = row
            
            # Decrypt and format DOB using the same logic as clients.py
            dob_formatted = 'N/A'  # Default
            if encrypted_dob and isinstance(encrypted_dob, bytes):
                try:
                    decrypted_dob_str = decrypt_data(encrypted_dob)
                    if decrypted_dob_str:
                        try:
                            dob_date_str = decrypted_dob_str.strip().split(' ')[0]  # Get only the date part
                            dob_date = None
                            for fmt in ('%Y-%m-%d', '%m/%d/%Y', '%Y%m%d'):  # Try common date formats
                                try:
                                    dob_date = datetime.strptime(dob_date_str, fmt).date()
                                    break
                                except ValueError:
                                    continue
                            
                            if dob_date:
                                dob_formatted = dob_date.strftime('%m/%d/%Y')
                            else:
                                logger.warning(f"Could not parse decrypted DOB date part '{dob_date_str}' for student {student_id}")
                        except Exception as parse_err:
                            logger.error(f"Error parsing decrypted DOB '{decrypted_dob_str}' for student {student_id}: {parse_err}")
                    else:
                        logger.warning(f"DOB decryption returned empty for student {student_id}")
                except Exception as decrypt_err:
                    logger.error(f"Error decrypting DOB for student {student_id}: {decrypt_err}")
            
            client_data.append({
                'id': student_id,
                'name': full_name,
                'dob': dob_formatted,
                'account': corporate_account or ''
            })
        
        # Get unique providers with their IDs, Role, and Address
        cursor.execute("""
            SELECT DISTINCT vs.therapistID, staff.FullName,
                   COALESCE(staff.R_Name, '') as Role,
                   COALESCE(staff.streetAddress, '') as Address
            FROM vwSessions vs
            LEFT JOIN vwStaff staff ON vs.therapistID = staff.LoginID
            WHERE staff.FullName IS NOT NULL 
            ORDER BY staff.FullName
        """)
        provider_data = []
        for row in cursor.fetchall():
            therapist_id, full_name, role, address = row
            provider_data.append({
                'id': therapist_id,
                'name': full_name,
                'title': role or '',
                'address': address or ''
            })
        
        cursor.close()
        return client_data, provider_data
    except Exception as e:
        logger.error(f"Error fetching autocomplete data: {str(e)}")
        return [], []


def _get_code_options(conn):
    """Get unique codes with their names and descriptions for dropdown."""
    try:
        cursor = conn.cursor()
        
        # Get unique codes with their descriptions
        cursor.execute("""
            SELECT DISTINCT vs.CodeID, vs.CodeNameAndDesc
            FROM vwSessions vs 
            WHERE vs.CodeID IS NOT NULL 
            AND vs.CodeNameAndDesc IS NOT NULL
            ORDER BY vs.CodeNameAndDesc
        """)
        
        code_options = []
        for row in cursor.fetchall():
            code_id, code_desc = row
            code_options.append({
                'id': code_id,
                'description': code_desc
            })
        
        cursor.close()
        return code_options
    except Exception as e:
        logger.error(f"Error fetching code options: {str(e)}")
        return []


def _get_account_options(conn):
    """Get corporate accounts for dropdown."""
    try:
        cursor = conn.cursor()
        
        # Get corporate accounts that have sessions, plus add "Triumph" as default option
        cursor.execute("""
            SELECT CA_ID, CA_Name FROM (
                SELECT DISTINCT ca.CA_ID, ca.CA_Name
                FROM tblCorporateAccounts ca
                INNER JOIN student s ON ca.CA_ID = s.CorpAccount
                INNER JOIN vwSessions vs ON s.studentID = vs.studentID
                WHERE ca.CA_Name IS NOT NULL
                UNION
                SELECT 0 as CA_ID, 'Triumph' as CA_Name
            ) combined_accounts
            ORDER BY CASE WHEN CA_ID = 0 THEN 0 ELSE 1 END, CA_Name
        """)
        
        account_options = []
        for row in cursor.fetchall():
            account_id, account_name = row
            account_options.append({
                'id': account_id,
                'name': account_name
            })
        
        cursor.close()
        return account_options
    except Exception as e:
        logger.error(f"Error fetching account options: {str(e)}")
        return []


def _get_case_manager_options(conn):
    """Get case managers for dropdown."""
    try:
        cursor = conn.cursor()
        
        # Get case managers from vwStudents
        cursor.execute("""
            SELECT DISTINCT CaseManager, CaseManagerName
            FROM vwStudents 
            WHERE CaseManager IS NOT NULL
            AND CaseManagerName IS NOT NULL
            ORDER BY CaseManagerName
        """)
        
        case_manager_options = []
        for row in cursor.fetchall():
            case_manager_id, case_manager_name = row
            case_manager_options.append({
                'id': case_manager_id,
                'name': case_manager_name
            })
        
        cursor.close()
        return case_manager_options
    except Exception as e:
        logger.error(f"Error fetching case manager options: {str(e)}")
        return []


def _get_session_status_options(conn):
    """Get session statuses with IDs for dropdown."""
    try:
        cursor = conn.cursor()
        
        # Get all session statuses with status_group = 'session'
        cursor.execute("""
            SELECT Status_ID, Status_Name
            FROM SessionStatus
            WHERE Status_Name IS NOT NULL
            AND status_group = 'session'
            ORDER BY Status_Name
        """)
        
        status_options = []
        for row in cursor.fetchall():
            status_id, status_name = row
            status_options.append({
                'id': status_id,
                'name': status_name
            })
        
        cursor.close()
        return status_options
    except Exception as e:
        logger.error(f"Error fetching session status options: {str(e)}")
        return []


def _get_billing_status_options(conn):
    """Get billing statuses with IDs for dropdown."""
    try:
        cursor = conn.cursor()
        
        # Get all billing statuses with status_group = 'billing'
        cursor.execute("""
            SELECT Status_ID, Status_Name
            FROM SessionStatus
            WHERE Status_Name IS NOT NULL
            AND status_group = 'billing'
            ORDER BY Status_Name
        """)
        
        status_options = []
        for row in cursor.fetchall():
            status_id, status_name = row
            status_options.append({
                'id': status_id,
                'name': status_name
            })
        
        cursor.close()
        return status_options
    except Exception as e:
        logger.error(f"Error fetching billing status options: {str(e)}")
        return []


def _get_sessions_data(conn, base_query, parameters, offset, per_page):
    """Get paginated sessions data."""
    paginated_query = f"""
        {base_query}
        ORDER BY vs.Sessdate DESC, vs.starttime DESC
        OFFSET ? ROWS 
        FETCH NEXT ? ROWS ONLY
    """
    
    # Add pagination parameters to the existing parameter list
    all_parameters = parameters + [offset, per_page]
    
    try:
        # For raw connections, use cursor with proper parameter formatting
        cursor = conn.cursor()
        cursor.execute(paginated_query, all_parameters)
        column_names = [desc[0] for desc in cursor.description]
        rows = cursor.fetchall()
        cursor.close()
        return [dict(zip(column_names, row)) for row in rows]
    except Exception as e:
        logger.error(f"Error fetching sessions data: {str(e)}")
        logger.error(f"Query was: {paginated_query}")
        logger.error(f"Parameters were: {all_parameters}")
        return []


def _export_sessions_csv(filters):
    """Export sessions data to CSV format."""
    logger.info("CSV export requested")
    
    try:
        with get_db_connection(timeout=60, operation_name="CSV export") as conn:
            # Build query for all matching sessions (no pagination for export)
            base_query, parameters = _build_sessions_query(filters)
            
            # Add ordering for consistent export
            export_query = f"""
                {base_query}
                ORDER BY vs.Sessdate ASC, vs.starttime ASC
            """
            
            # Execute query
            cursor = conn.cursor()
            cursor.execute(export_query, parameters)
            column_names = [desc[0] for desc in cursor.description]
            rows = cursor.fetchall()
            cursor.close()
            
            # Create CSV in memory
            output = io.StringIO()
            writer = csv.writer(output)
            
            # Write header row
            writer.writerow(column_names)
            
            # Write data rows
            for row in rows:
                # Convert any None values to empty strings for CSV
                csv_row = [str(cell) if cell is not None else '' for cell in row]
                writer.writerow(csv_row)
            
            # Get CSV content
            csv_content = output.getvalue()
            output.close()
            
            # Generate filename with date range
            from_date = filters.get('from_date', 'unknown')
            to_date = filters.get('to_date', 'unknown')
            filename = f"sessions_{from_date}_to_{to_date}.csv"
            
            logger.info(f"CSV export completed: {len(rows)} sessions exported")
            
            # Return CSV response
            return Response(
                csv_content,
                mimetype='text/csv',
                headers={
                    'Content-Disposition': f'attachment; filename="{filename}"'
                }
            )
            
    except Exception as e:
        logger.error(f"Error during CSV export: {str(e)}")
        flash('Error exporting CSV. Please try again.', 'error')
        return redirect(url_for('sessions.sessions', **request.args))


def _validate_filters(filters):
    """Validate filter parameters."""
    errors = []
    
    # Validate dates
    if filters.get('from_date'):
        try:
            datetime.strptime(filters['from_date'], '%Y-%m-%d')
        except ValueError:
            errors.append("Invalid from_date format. Use YYYY-MM-DD")
    
    if filters.get('to_date'):
        try:
            datetime.strptime(filters['to_date'], '%Y-%m-%d')
        except ValueError:
            errors.append("Invalid to_date format. Use YYYY-MM-DD")
    
    # Validate session IDs
    if filters.get('session_id'):
        try:
            _parse_session_ids(filters['session_id'])
        except ValueError as e:
            errors.append(str(e))
    
    # Validate numeric fields
    for field in ['code_id', 'account', 'case_manager']:
        if filters.get(field):
            try:
                [int(x) for x in filters[field] if x]
            except ValueError:
                errors.append(f"Invalid {field} format")
    
    return errors


def _log_session_access(user_id, filters, result_count, error=None):
    """Structured logging for session access."""
    log_data = {
        'user_id': user_id,
        'filters': filters,
        'result_count': result_count,
        'timestamp': datetime.utcnow().isoformat(),
        'cache_version': CACHE_VERSION
    }
    
    if error:
        log_data['error'] = str(error)
        logger.error("Session access error", extra=log_data)
    else:
        logger.info("Session access", extra=log_data)


@sessions_bp.route('/')
@login_required
@check_permission('session.view')
@cache.cached(timeout=300, key_prefix='sessions-page', 
              unless=lambda: request.method == 'POST' or 'search' in request.args or 'export' in request.args)
def sessions():
    """Sessions list page with filtering and pagination."""
    
    logger.info("Sessions page accessed")
    
    # Initialize variables
    sessions_data = []
    total_sessions = 0
    error_message = None
    client_names = []
    provider_names = []
    code_options = []
    
    # Get user ID from session
    user_id = session.get('user_id')
    if not user_id:
        logger.warning("No user_id found in session")
        flash('Session expired. Please log in again.', 'error')
        return redirect(url_for('auth.login'))
    
    # Pagination settings
    page = int(request.args.get('page', 1))
    per_page = 25
    offset = (page - 1) * per_page
    
    # Get filter parameters - handle multiple selections for non-date fields
    filters = {
        'from_date': request.args.get('from_date', '').strip() or None,
        'to_date': request.args.get('to_date', '').strip() or None,
        'session_status': request.args.getlist('session_status'),
        'billing_status': request.args.getlist('billing_status'),
        'client': request.args.getlist('client'),
        'provider': request.args.getlist('provider'),
        'account': request.args.getlist('account'),
        'session_id': request.args.get('session_id', ''),
        'code_id': request.args.getlist('code_id'),
        'case_manager': request.args.getlist('case_manager')
    }
    
    # Validate filters
    validation_errors = _validate_filters(filters)
    if validation_errors:
        error_message = "\n".join(validation_errors)
        _log_session_access(user_id, filters, 0, error=error_message)
        flash(error_message, 'error')
        return render_template('sessions.html',
                             error_message=error_message,
                             filters=filters,
                             should_load_data=False)
    
    # Check if we should load data (search button clicked)
    search_clicked = request.args.get('search') == 'true'
    has_date_range = filters['from_date'] and filters['to_date']
    
    # Validate date range if both dates are provided
    date_range_valid = True
    if has_date_range:
        try:
            from_date_obj = datetime.strptime(filters['from_date'], '%Y-%m-%d')
            to_date_obj = datetime.strptime(filters['to_date'], '%Y-%m-%d')
            if from_date_obj > to_date_obj:
                date_range_valid = False
                error_message = "From date cannot be later than To date."
                logger.warning(f"Invalid date range: {filters['from_date']} to {filters['to_date']}")
        except ValueError as e:
            date_range_valid = False
            error_message = "Invalid date format. Please use YYYY-MM-DD format."
            logger.warning(f"Invalid date format provided: {e}")
    
    should_load_data = search_clicked and date_range_valid
    
    # Check if CSV export is requested
    export_csv = request.args.get('export') == 'csv'
    
    # Handle CSV export if requested and data is valid
    if export_csv:
        logger.info(f"CSV export requested: date_range_valid={date_range_valid}")
        if date_range_valid:
            try:
                logger.info("CSV export conditions met, proceeding with export")
                return _export_sessions_csv(filters)
            except Exception as e:
                logger.error(f"CSV export error: {str(e)}")
                flash('Error exporting CSV. Please try again.', 'error')
                return redirect(url_for('sessions.sessions', **{k: v for k, v in request.args.items() if k != 'export'}))
        else:
            logger.warning("CSV export failed: invalid date range")
            flash('Please check your date range - From Date cannot be later than To Date.', 'error')
            return redirect(url_for('sessions.sessions', **{k: v for k, v in request.args.items() if k != 'export'}))
    
    # Always fetch autocomplete data for client, provider, code, account, and case manager filters
    try:
        with get_db_connection(timeout=10, operation_name="autocomplete data") as conn:
            client_names, provider_names = _get_autocomplete_data(conn)
            code_options = _get_code_options(conn)
            account_options = _get_account_options(conn)
            case_manager_options = _get_case_manager_options(conn)
            session_status_options = _get_session_status_options(conn)
            billing_status_options = _get_billing_status_options(conn)
    except DatabaseError as e:
        logger.error(f"Database error fetching autocomplete data: {str(e)}")
        flash('Error loading filter options. Please try again.', 'error')
        return redirect(url_for('sessions.sessions'))
    except Exception as e:
        logger.error(f"Unexpected error fetching autocomplete data: {str(e)}")
        flash('An unexpected error occurred. Please try again.', 'error')
        return redirect(url_for('sessions.sessions'))
    
    if should_load_data:
        # Create cache key based on filters and pagination
        cache_key = make_cache_key('sessions-data', 
                                 filters=filters, 
                                 page=page, 
                                 per_page=per_page,
                                 version=CACHE_VERSION)
        
        # Try to get cached result first
        cached_result = cache.get(cache_key)
        if cached_result:
            logger.debug("Using cached sessions data")
            sessions_data, total_sessions = cached_result
        else:
            try:
                with get_db_connection(timeout=30, operation_name="sessions query") as conn:
                    # Build optimized query
                    base_query, parameters = _build_sessions_query(filters)
                    
                    # Get total count for pagination
                    total_sessions = _get_sessions_count(conn, base_query, parameters)
                    
                    # Get paginated data
                    if total_sessions > 0:
                        sessions_data = _get_sessions_data(conn, base_query, parameters, offset, per_page)
                    
                    # Cache the result for 5 minutes
                    cache.set(cache_key, (sessions_data, total_sessions), timeout=300)
                    logger.info(f"Sessions query completed: {len(sessions_data)} sessions found")
                    
            except DatabaseError as e:
                logger.error(f"Database error fetching sessions data: {str(e)}")
                error_message = "Database error occurred. Please try again."
                sessions_data = []
                total_sessions = 0
            except Exception as e:
                logger.error(f"Unexpected error fetching sessions data: {str(e)}")
                error_message = "An unexpected error occurred. Please try again."
                sessions_data = []
                total_sessions = 0
    
    # Log the session access
    _log_session_access(user_id, filters, len(sessions_data), error=error_message)
    
    # Calculate pagination info
    total_pages = ceil(total_sessions / per_page) if total_sessions > 0 else 1
    
    # Prepare pagination data
    pagination = {
        'page': page,
        'per_page': per_page,
        'total': total_sessions,
        'pages': total_pages,
        'has_prev': page > 1,
        'has_next': page < total_pages,
        'prev_num': page - 1 if page > 1 else None,
        'next_num': page + 1 if page < total_pages else None
    }
    
    return render_template('sessions.html', 
                         sessions=sessions_data,
                         pagination=pagination,
                         error_message=error_message,
                         filters=filters,
                         should_load_data=should_load_data,
                         client_names=client_names,
                         provider_names=provider_names,
                         code_options=code_options,
                         account_options=account_options,
                         case_manager_options=case_manager_options,
                         session_status_options=session_status_options,
                         billing_status_options=billing_status_options)


def get_optimization_suggestions():
    """
    Generate SQL suggestions for optimizing sessions queries.
    These should be reviewed and applied by a DBA.
    
    Returns:
        list: SQL statements for performance optimization
    """
    suggestions = [
        "-- Sessions Performance Optimization Suggestions",
        "-- Review and apply these with your DBA:",
        "",
        "-- 1. Main date range index for sessions",
        "CREATE NONCLUSTERED INDEX IX_Sessions_Date_Performance",
        "ON [dbo].[Sessions] (Sessdate DESC, starttime DESC)",
        "INCLUDE (therapistID, Status_Name, BillingStatusName, LocationID, CodeID, payrollstatus, DateSubmitted);",
        "",
        "-- 2. Provider filtering index",
        "CREATE NONCLUSTERED INDEX IX_Sessions_Provider_Date",
        "ON [dbo].[Sessions] (therapistID, Sessdate DESC);",
        "",
        "-- 3. Status filtering index",
        "CREATE NONCLUSTERED INDEX IX_Sessions_Status",
        "ON [dbo].[Sessions] (Status_Name, BillingStatusName)",
        "INCLUDE (Sessdate, therapistID);",
        "",
        "-- 4. Staff name search index", 
        "CREATE NONCLUSTERED INDEX IX_Staff_FullName",
        "ON [dbo].[Staff] (FullName)",
        "INCLUDE (LoginID);",
        "",
        "-- 5. Update statistics for views",
        "UPDATE STATISTICS vwSessions;",
        "UPDATE STATISTICS vwStaff;",
        "",
        "-- Note: Test these on a dev environment first!",
        "-- Monitor query execution plans before and after."
    ]
    return suggestions 