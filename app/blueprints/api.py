import logging
from datetime import datetime, date
from flask import Blueprint, jsonify, request, session
from sqlalchemy.exc import IntegrityError, SQLAlchemyError

from app.auth import login_required
from app.middleware.permission_check import check_permission
from app.models import db, PhoneLog, CallType, User
from app.services.phone_log_service import PhoneLogService
from app.services.guided_form_service import GuidedFormService
from app.responses.phone_log_responses import GetPendingCallsResponse

# Create the blueprint with API prefix
api_bp = Blueprint('api', __name__, url_prefix='/api/v1')

logger = logging.getLogger(__name__)


@api_bp.route('/students/by-case-manager', methods=['GET'])
@login_required
@check_permission('client.view')
def get_students_for_case_manager():
    """Get all students assigned to the current case manager or specified therapist."""
    try:
        user_id = session.get('user_id')
        username = session.get('username', 'Unknown')
        
        # Check if a specific therapist_id is requested
        therapist_id = request.args.get('therapist_id')
        
        if therapist_id:
            logger.info(f"Getting students for therapist ID: {therapist_id}")
            target_id = therapist_id
        else:
            logger.info(f"Getting students for case manager {username} (ID: {user_id})")
            target_id = username
        
        # Map user to MSSQL LoginID based on username
        from app.database import get_read_only_connection
        
        students = []
        
        try:
            with get_read_only_connection() as conn:
                # Get students for this case manager
                # Note: s.CaseManager should match staff.LoginID
                students_query = """
                    SELECT s.StudentID, s.FirstName, s.LastName, s.DOB,
                           h.h_mothername, h.h_mothercell,
                           h.h_fathername, h.h_fathercell,
                           h.h_homephone
                    FROM Student s
                    LEFT JOIN Staff st ON s.CaseManager = st.LoginID
                    LEFT JOIN tblHomes h ON s.HomeID = h.h_LoginID
                    WHERE st.LoginID = ?
                       OR s.CaseManager = ?
                    ORDER BY s.LastName, s.FirstName
                """
                
                # Use target_id (either current user or specified therapist)
                student_results = conn.execute(students_query, (target_id, target_id)).fetchall()
                
                for row in student_results:
                    student_id = row[0]
                    first_name = row[1] 
                    last_name = row[2]
                    dob = row[3]
                    
                    # Build parent contacts
                    parent_contacts = []
                    
                    # Add mother if exists
                    if row[4]:  # h_mothername
                        mother_phone = row[5] or row[8]  # h_mothercell or h_homephone
                        if mother_phone:
                            parent_contacts.append({
                                'relationship': 'Mother',
                                'name': row[4],
                                'phone': mother_phone
                            })
                    
                    # Add father if exists
                    if row[6]:  # h_fathername
                        father_phone = row[7] or row[8]  # h_fathercell or h_homephone
                        if father_phone:
                            parent_contacts.append({
                                'relationship': 'Father', 
                                'name': row[6],
                                'phone': father_phone
                            })
                    
                    # If no parent contacts, add generic ones
                    if not parent_contacts:
                        parent_contacts = [
                            {'relationship': 'Guardian', 'name': 'Primary Guardian', 'phone': '(*************'}
                        ]
                    
                    students.append({
                        'id': student_id,
                        'name': f"{last_name}, {first_name}",
                        'first_name': first_name,
                        'last_name': last_name,
                        'dob': dob,
                        'parent_contacts': parent_contacts
                    })
                    
        except Exception as db_error:
            logger.warning(f"Could not fetch students from MSSQL: {str(db_error)}")
            # Return empty list if database is unavailable
            students = []
        
        logger.info(f"Retrieved {len(students)} students for case manager {username}")
        
        return jsonify({
            'students': students,
            'count': len(students)
        }), 200
        
    except Exception as e:
        logger.error(f"Error getting students for case manager: {str(e)}")
        return jsonify({
            "error": "Failed to fetch students",
            "message": str(e)
        }), 500


@api_bp.route('/clients/<int:client_id>/phone_logs', methods=['POST'])
@login_required
# TODO: Add proper permission check once admin user has client.edit permission
# @check_permission('client.edit')
def create_phone_log(client_id):
    """
    Create a new phone log entry for a client.
    
    Expected JSON payload:
    {
        "call_date": "2025-01-15",
        "reached_out_to": "Mother",
        "call_type_id": 1,
        "notes_content": "Discussed therapy progress...",
        "prompt_responses": {"question1": "answer1"}
    }
    """
    try:
        # Validate JSON payload
        data = request.get_json()
        if not data:
            return jsonify({'error': 'Invalid JSON payload'}), 400
        
        # Validate required fields
        required_fields = ['call_date', 'reached_out_to']
        errors = []
        
        for field in required_fields:
            if field not in data or not data[field]:
                errors.append(f'{field} is required')
        
        if errors:
            return jsonify({'error': 'Validation failed', 'details': errors}), 400
        
        # Validate and parse call_date
        try:
            if isinstance(data['call_date'], str):
                call_date = datetime.strptime(data['call_date'], '%Y-%m-%d').date()
            else:
                call_date = data['call_date']
        except ValueError:
            return jsonify({'error': 'call_date must be in YYYY-MM-DD format'}), 400
        
        # Validate call_type_id if provided
        call_type_id = data.get('call_type_id')
        if call_type_id:
            call_type = CallType.query.get(call_type_id)
            if not call_type:
                return jsonify({'error': f'Invalid call_type_id: {call_type_id}'}), 400
        
        # Get current user from session
        user_id = session.get('user_id')
        if not user_id:
            return jsonify({'error': 'User not authenticated'}), 401
        
        # Create the phone log entry
        phone_log = PhoneLog.create_log(
            client_id=client_id,
            user_id=user_id,
            call_date=call_date,
            reached_out_to=data['reached_out_to'].strip(),
            call_type_id=call_type_id,
            notes_content=data.get('notes_content', '').strip() if data.get('notes_content') else None,
            prompt_responses=data.get('prompt_responses', {})
        )
        
        # Save to database
        db.session.add(phone_log)
        db.session.commit()
        
        # Refresh the materialized view to update pending calls
        phone_log_service = PhoneLogService()
        try:
            phone_log_service.refresh_pending_calls_view()
        except Exception as e:
            logger.warning(f"Could not refresh materialized view: {str(e)}")
            # Don't fail the request if view refresh fails
        
        logger.info(f"Phone log created: ID={phone_log.id}, Client={client_id}, User={user_id}")
        
        return jsonify({
            'message': 'Phone log created successfully',
            'phone_log': phone_log.to_dict()
        }), 201
        
    except ValueError as e:
        db.session.rollback()
        logger.warning(f"Validation error creating phone log: {str(e)}")
        return jsonify({'error': str(e)}), 400
        
    except IntegrityError as e:
        db.session.rollback()
        logger.error(f"Database integrity error creating phone log: {str(e)}")
        return jsonify({'error': 'Database constraint violation'}), 400
        
    except SQLAlchemyError as e:
        db.session.rollback()
        logger.error(f"Database error creating phone log: {str(e)}")
        return jsonify({'error': 'Database error occurred'}), 500
        
    except Exception as e:
        db.session.rollback()
        logger.error(f"Unexpected error creating phone log: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500


@api_bp.route('/clients/<int:client_id>/phone_log_meta', methods=['GET'])
@login_required
# TODO: Add proper permission check once admin user has client.view permission
# @check_permission('client.view')
def get_phone_log_meta(client_id):
    """
    Get metadata needed for phone log form.
    
    Returns:
    {
        "client_info": {
            "client_id": 123,
            "name": "John Doe",
            "dob": "2015-05-15"
        },
        "parent_contacts": [
            {"name": "Jane Doe", "phone": "************", "relationship": "Mother"},
            {"name": "Bob Doe", "phone": "************", "relationship": "Father"}
        ],
        "call_types": [
            {"id": 1, "name": "Initial Contact", "prompt_questions": ["Q1", "Q2"]},
            {"id": 2, "name": "Follow-up", "prompt_questions": ["Q1", "Q2"]}
        ]
    }
    """
    try:
        # Note: In this implementation, we're focusing on call_types since 
        # client info and parent contacts require complex MSSQL queries
        # This will be expanded in the next subtask
        
        # Get all available call types
        call_types = CallType.query.order_by(CallType.name).all()
        call_types_data = [call_type.to_dict() for call_type in call_types]
        
        # Fetch real client data from MSSQL
        from app.database import get_read_only_connection
        
        client_info = {
            "client_id": client_id,
            "name": "TBD - Requires MSSQL query",
            "dob": None
        }
        parent_contacts = []
        
        try:
            with get_read_only_connection() as conn:
                # Get client basic info
                client_query = """
                    SELECT s.FirstName, s.LastName, s.DOB
                    FROM Student s
                    WHERE s.StudentID = ?
                """
                client_result = conn.execute(client_query, (client_id,)).fetchone()
                
                if client_result:
                    client_info = {
                        "client_id": client_id,
                        "name": f"{client_result[1]}, {client_result[0]}",  # LastName, FirstName
                        "dob": client_result[2]  # Will need decryption later
                    }
                
                # Get parent/guardian contacts from Users table via HomeID
                parent_query = """
                    SELECT h.h_mothername, h.h_mothercell,
                           h.h_fathername, h.h_fathercell,
                           h.h_homephone
                    FROM Student s
                    LEFT JOIN tblHomes h ON s.HomeID = h.h_LoginID
                    WHERE s.StudentID = ?
                """
                parent_result = conn.execute(parent_query, (client_id,)).fetchone()
                
                if parent_result:
                    # Add mother contact if exists
                    if parent_result[0]:  # h_mothername
                        mother_phone = parent_result[1] or parent_result[4]  # h_mothercell or h_homephone
                        if mother_phone:
                            parent_contacts.append({
                                'relationship': 'Mother',
                                'name': parent_result[0],
                                'phone': mother_phone
                            })
                    
                    # Add father contact if exists
                    if parent_result[2]:  # h_fathername
                        father_phone = parent_result[3] or parent_result[4]  # h_fathercell or h_homephone
                        if father_phone:
                            parent_contacts.append({
                                'relationship': 'Father',
                                'name': parent_result[2],
                                'phone': father_phone
                            })
                
                # If no specific parent contacts found, add default options
                if not parent_contacts:
                    parent_contacts = [
                        {'relationship': 'Guardian', 'name': 'Primary Guardian', 'phone': '(*************'},
                        {'relationship': 'Parent', 'name': 'Parent/Guardian', 'phone': '(*************'}
                    ]
                    
        except Exception as db_error:
            logger.warning(f"Could not fetch client data from MSSQL: {str(db_error)}")
            # Use fallback data
            parent_contacts = [
                {'relationship': 'Guardian', 'name': 'Primary Guardian', 'phone': '(*************'},
                {'relationship': 'Parent', 'name': 'Parent/Guardian', 'phone': '(*************'}
            ]
        
        meta_data = {
            "client_info": client_info,
            "parent_contacts": parent_contacts,
            "call_types": call_types_data
        }
        
        logger.info(f"Phone log metadata retrieved for client {client_id}")
        
        return jsonify(meta_data), 200
        
    except SQLAlchemyError as e:
        logger.error(f"Database error fetching phone log metadata: {str(e)}")
        return jsonify({'error': 'Database error occurred'}), 500
        
    except Exception as e:
        logger.error(f"Unexpected error fetching phone log metadata: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500


@api_bp.route('/therapists', methods=['GET'])
@login_required
@check_permission('client.view')
def get_all_therapists():
    """Get all therapists from the Staff table."""
    try:
        from app.database import get_read_only_connection
        
        therapists = []
        
        try:
            with get_read_only_connection() as conn:
                # Get all therapists from Staff table
                # Filter only active staff members who have students assigned
                therapists_query = """
                    SELECT DISTINCT 
                        st.LoginID,
                        st.FirstName,
                        st.LastName,
                        st.UserName,
                        COUNT(DISTINCT tsl.StudentID) as student_count
                    FROM Staff st
                    INNER JOIN therapistStudentLink tsl ON st.LoginID = tsl.TherapistID
                    WHERE tsl.EndDate IS NULL OR tsl.EndDate >= GETDATE()
                    AND st.Active = 1
                    GROUP BY st.LoginID, st.FirstName, st.LastName, st.UserName
                    ORDER BY st.LastName, st.FirstName
                """
                
                therapist_results = conn.execute(therapists_query).fetchall()
                
                for row in therapist_results:
                    therapists.append({
                        'id': row[0],
                        'first_name': row[1],
                        'last_name': row[2],
                        'username': row[3],
                        'name': f"{row[2]}, {row[1]}",  # LastName, FirstName
                        'student_count': row[4]
                    })
                    
        except Exception as db_error:
            logger.warning(f"Could not fetch therapists from MSSQL: {str(db_error)}")
            # Return empty list if database is unavailable
            therapists = []
        
        logger.info(f"Retrieved {len(therapists)} therapists")
        
        return jsonify({
            'therapists': therapists,
            'count': len(therapists)
        }), 200
        
    except Exception as e:
        logger.error(f"Error getting therapists: {str(e)}")
        return jsonify({
            "error": "Failed to fetch therapists",
            "message": str(e)
        }), 500


@api_bp.route('/phonelog/pending', methods=['GET'])
@login_required
@check_permission('client.view')
def get_pending_phone_calls():
    """
    Get list of students who need phone calls.
    
    Query parameters:
    - days_threshold: Number of days before a student needs another call (default: 7)
    - user_id: Optional user ID to filter by case manager (defaults to current user)
    
    Returns:
    {
        "data": [
            {
                "student_id": 123,
                "name": "Doe, John",
                "first_name": "John",
                "last_name": "Doe",
                "dob": "2015-05-15",
                "case_manager": "therapist1",
                "last_call_date": "2025-01-01" or null,
                "days_since_last_call": 10 or null,
                "total_calls": 3,
                "priority": "never_called" or "overdue",
                "parent_contacts": [...]
            }
        ],
        "summary": {
            "total_pending": 25,
            "never_called": 10,
            "overdue": 15
        },
        "count": 25,
        "timestamp": "2025-01-15T10:30:00",
        "message": "Pending phone calls retrieved successfully"
    }
    """
    try:
        # Get query parameters
        days_threshold = request.args.get('days_threshold', 7, type=int)
        requested_user_id = request.args.get('user_id', type=int)
        
        # Determine which user to filter by
        current_user_id = session.get('user_id')
        if requested_user_id and requested_user_id != current_user_id:
            # Check if current user has permission to view other users' data
            # For now, allow case managers to view all data
            filter_user_id = requested_user_id
        else:
            filter_user_id = current_user_id
        
        # Create service instance and get pending calls
        phone_log_service = PhoneLogService()
        pending_students = phone_log_service.getPendingCalls(
            user_id=filter_user_id,
            days_threshold=days_threshold
        )
        
        # Create response object
        response = GetPendingCallsResponse(pending_students)
        
        logger.info(f"Pending phone calls retrieved: {response.total_count} students")
        
        # Return JSON response
        return jsonify(response.toJson()), 200
        
    except ValueError as e:
        logger.warning(f"Validation error getting pending calls: {str(e)}")
        return jsonify({'error': str(e)}), 400
        
    except SQLAlchemyError as e:
        logger.error(f"Database error getting pending calls: {str(e)}")
        return jsonify({'error': 'Database error occurred'}), 500
        
    except Exception as e:
        logger.error(f"Unexpected error getting pending calls: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500


@api_bp.route('/health', methods=['GET'])
def api_health():
    """Health check endpoint for the API."""
    return jsonify({
        'status': 'healthy',
        'version': 'v1',
        'timestamp': datetime.utcnow().isoformat()
    }), 200


# ========================================
# GuidedForm API Endpoints
# ========================================

@api_bp.route('/guided-form/prompts/<prompt_id>', methods=['GET'])
@login_required
@check_permission('client.view')
def get_guide_prompt(prompt_id):
    """
    Get a guide prompt configuration by ID.
    
    Args:
        prompt_id: Unique identifier for the guide prompt
        
    Returns:
        Guide prompt configuration with templates and schema
    """
    try:
        guided_form_service = GuidedFormService()
        prompt = guided_form_service.get_guide_prompt(prompt_id)
        
        if not prompt:
            return jsonify({'error': f'Guide prompt {prompt_id} not found'}), 404
        
        logger.info(f"Guide prompt retrieved: {prompt_id}")
        return jsonify(prompt), 200
        
    except Exception as e:
        logger.error(f"Error retrieving guide prompt {prompt_id}: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500


@api_bp.route('/guided-form/guidance', methods=['POST'])
@login_required
@check_permission('client.view')
def generate_guidance():
    """
    Generate AI guidance for a form based on prompt and context.
    
    Request body:
    {
        "promptId": "phone-log",
        "context": {
            "studentId": 123,
            "userId": 456,
            "additionalData": "..."
        }
    }
    
    Returns:
        Generated guidance including prompts, suggestions, and context summary
    """
    try:
        data = request.get_json()
        
        if not data:
            return jsonify({'error': 'Request body required'}), 400
        
        prompt_id = data.get('promptId')
        context = data.get('context', {})
        
        if not prompt_id:
            return jsonify({'error': 'promptId is required'}), 400
        
        # Add current user to context if not provided
        if 'userId' not in context:
            context['userId'] = session.get('user_id')
        
        # Generate guidance using service
        guided_form_service = GuidedFormService()
        guidance = guided_form_service.generate_guidance(prompt_id, context)
        
        logger.info(f"Guidance generated for prompt: {prompt_id}")
        return jsonify(guidance), 200
        
    except ValueError as e:
        logger.warning(f"Validation error generating guidance: {str(e)}")
        return jsonify({'error': str(e)}), 400
        
    except Exception as e:
        logger.error(f"Error generating guidance: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500


@api_bp.route('/guided-form/process', methods=['POST'])
@login_required
@check_permission('client.view')
def process_form_input():
    """
    Process user input through LLM to extract structured data.
    
    Request body:
    {
        "promptId": "phone-log",
        "context": {
            "studentId": 123,
            "userId": 456
        },
        "input": "Had a great conversation with the parent..."
    }
    
    Returns:
        Processed response with structured data and summary
    """
    try:
        data = request.get_json()
        
        if not data:
            return jsonify({'error': 'Request body required'}), 400
        
        prompt_id = data.get('promptId')
        context = data.get('context', {})
        user_input = data.get('input')
        
        if not prompt_id:
            return jsonify({'error': 'promptId is required'}), 400
        
        if not user_input:
            return jsonify({'error': 'input is required'}), 400
        
        # Add current user to context if not provided
        if 'userId' not in context:
            context['userId'] = session.get('user_id')
        
        # Process input using service
        guided_form_service = GuidedFormService()
        processed_response = guided_form_service.process_input(prompt_id, context, user_input)
        
        logger.info(f"Input processed for prompt: {prompt_id}, length: {len(user_input)} chars")
        return jsonify(processed_response), 200
        
    except ValueError as e:
        logger.warning(f"Validation error processing input: {str(e)}")
        return jsonify({'error': str(e)}), 400
        
    except Exception as e:
        logger.error(f"Error processing input: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500


@api_bp.route('/guided-form/submit', methods=['POST'])
@login_required
@check_permission('client.view')
def submit_guided_form():
    """
    Submit completed form data for storage.
    
    Request body:
    {
        "promptId": "phone-log",
        "context": {
            "studentId": 123,
            "userId": 456
        },
        "formData": {
            "rawInput": "...",
            "structuredData": {...},
            "summary": "...",
            "actionItems": [...],
            "metadata": {...}
        }
    }
    
    Returns:
        Success confirmation
    """
    try:
        data = request.get_json()
        
        if not data:
            return jsonify({'error': 'Request body required'}), 400
        
        prompt_id = data.get('promptId')
        context = data.get('context', {})
        form_data = data.get('formData', {})
        
        if not prompt_id:
            return jsonify({'error': 'promptId is required'}), 400
        
        if not form_data:
            return jsonify({'error': 'formData is required'}), 400
        
        # Add current user to context if not provided
        if 'userId' not in context:
            context['userId'] = session.get('user_id')
        
        # Save form data using service
        guided_form_service = GuidedFormService()
        success = guided_form_service.save_form_data(prompt_id, context, form_data)
        
        if success:
            logger.info(f"Form data saved for prompt: {prompt_id}")
            return jsonify({
                'message': 'Form submitted successfully',
                'timestamp': datetime.utcnow().isoformat()
            }), 200
        else:
            logger.error(f"Failed to save form data for prompt: {prompt_id}")
            return jsonify({'error': 'Failed to save form data'}), 500
        
    except ValueError as e:
        logger.warning(f"Validation error submitting form: {str(e)}")
        return jsonify({'error': str(e)}), 400
        
    except Exception as e:
        logger.error(f"Error submitting form: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500


@api_bp.route('/guided-form/context/<prompt_id>', methods=['POST'])
@login_required
@check_permission('client.view')
def get_enhanced_context(prompt_id):
    """
    Get enhanced context data for a specific prompt and base context.
    
    Request body:
    {
        "context": {
            "studentId": 123,
            "additionalData": "..."
        }
    }
    
    Returns:
        Enhanced context with data from multiple sources
    """
    try:
        data = request.get_json()
        
        if not data:
            return jsonify({'error': 'Request body required'}), 400
        
        context = data.get('context', {})
        
        # Add current user to context if not provided
        if 'userId' not in context:
            context['userId'] = session.get('user_id')
        
        # Get enhanced context using service
        guided_form_service = GuidedFormService()
        enhanced_context = guided_form_service.aggregate_context(prompt_id, context)
        
        logger.info(f"Enhanced context retrieved for prompt: {prompt_id}")
        return jsonify({
            'context': enhanced_context,
            'timestamp': datetime.utcnow().isoformat()
        }), 200
        
    except Exception as e:
        logger.error(f"Error getting enhanced context for {prompt_id}: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500 