import logging
import time
from datetime import datetime, timedelta
from contextlib import contextmanager
from flask import Blueprint, render_template, jsonify, request, session, redirect, url_for
from sqlalchemy import text
from app.auth import login_required
from app.database import get_read_only_connection
from app.middleware.permission_check import check_permission
from app import cache, make_cache_key

# Create blueprint with url_prefix
payroll_bp = Blueprint('payroll', __name__, url_prefix='/payroll')

@payroll_bp.route('/dashboard')
@login_required
@cache.cached(timeout=3600, key_prefix=make_cache_key)  # User-specific cache with 1-hour timeout
@check_permission('payroll.view')
def payroll_dashboard():
    """Payroll dashboard with charts, split into Trends and Period Details."""
    logging.info("Accessed /payroll-dashboard route")

    # --- Get filter parameters --- 
    num_periods_to_show = 12  # For trend charts
    selected_provider_id = request.args.get('provider_id', default=None, type=int)
    selected_detail_period_id = request.args.get('detail_period_id', type=int)  # Re-introduced for detail section
    top_n_providers = 5
    # Define agency_id from session or use default
    agency_id = session.get('agency_id', 1)  # Default to 1 if not in session
    
    conn = None
    chart_data = {}
    latest_period_id = None
    pay_periods_for_filter = []  # Re-introduced for detail period dropdown
    providers_for_filter = []
    error_message = None

    try:
        conn = get_read_only_connection()
        logging.info("Database connection established for payroll dashboard.")

        # --- Fetch Pay Periods List for Detail Filter & Determine Default --- 
        try:
            pay_periods_query = text(
                "SELECT PP_ID, FORMAT(PP_StartDate, 'MMM dd yyyy') + ' - ' + FORMAT(PP_EndDate, 'MMM dd yyyy') AS PeriodRange FROM tblPayPeriod ORDER BY PP_StartDate DESC")
            pay_periods_result = conn.execute(str(pay_periods_query)).fetchall()
            pay_periods_for_filter = [(row.PP_ID, row.PeriodRange) for row in pay_periods_result]
            logging.info(f"Fetched {len(pay_periods_for_filter)} pay periods for detail filter.")

            # Determine the period ID to use for details
            if selected_detail_period_id:
                logging.info(f"Using selected detail_period_id: {selected_detail_period_id}")
            elif pay_periods_for_filter:  # If none selected, use the latest one
                selected_detail_period_id = pay_periods_for_filter[0][0]
                latest_period_id = selected_detail_period_id  # Store latest ID if needed elsewhere
                logging.info(f"Defaulting detail_period_id to latest: {selected_detail_period_id}")
            else:
                logging.error("No pay periods found to select for details.")
                error_message = "No pay periods found."
        except Exception as e:
            logging.error(f"Error fetching pay periods list: {e}")
            error_message = (error_message + " | " if error_message else "") + "Could not fetch pay periods."

        # --- Fetch Providers for Filter --- 
        try:
            providers_query = text(
                "SELECT LoginID, FullName FROM vwStaff WHERE isActive = 1 ORDER BY LastName, FirstName")
            providers_sql = str(providers_query)
            providers_result = conn.execute(providers_sql).fetchall()
            providers_for_filter = [(row[0], row[1]) for row in providers_result]
            logging.info(f"Fetched {len(providers_for_filter)} active providers for filter dropdown.")
        except Exception as e:
            logging.error(f"Error fetching providers for filter: {e}")
            error_message = (
                                error_message + " | " if error_message else "") + "Could not fetch providers for filtering."

        # --- Fetch Aggregated Data for Charts (Applying Filters) ---
        logging.info(
            f"Dashboard Filters - ProviderID: {selected_provider_id}, DetailPeriodID: {selected_detail_period_id}")

        # --- Trend Charts Data (Apply Provider Filter) ---
        # 1. Pay/Hours Trend (Last N, Filter Provider)
        try:
            logging.info(
                f"Fetching trend data for last {num_periods_to_show} periods (Provider: {selected_provider_id or 'All'}).")
            # ... Trend query using selected_provider_id (same as before) ...
            trend_query = text(f"""
                WITH RecentPeriods AS (
                    SELECT TOP {num_periods_to_show}
                        PP_ID,
                        FORMAT(PP_StartDate, 'MMM yyyy') as PeriodLabel
                    FROM tblPayPeriod
                    ORDER BY PP_StartDate DESC
                )
                SELECT
                    rp.PeriodLabel,
                    ISNULL(SUM(si.TotalHours), 0) as TotalHours,
                    ISNULL(SUM(si.TotalPay), 0) as TotalSubmittedPay
                FROM RecentPeriods rp
                LEFT JOIN SessionInvoice si ON rp.PP_ID = si.PayPeriodID
                                         AND (? IS NULL OR si.ProviderID = ?) -- Provider Filter
                GROUP BY rp.PP_ID, rp.PeriodLabel
                ORDER BY rp.PP_ID ASC;
            """)
            trend_params = (selected_provider_id, selected_provider_id)
            trend_result = conn.execute(str(trend_query), trend_params).fetchall()
            logging.info(f"Fetched {len(trend_result)} rows for trend chart.")
            chart_data['trend'] = {
                'labels': [row.PeriodLabel for row in trend_result],
                'hours': [float(row.TotalHours) for row in trend_result],
                'pay': [float(row.TotalSubmittedPay) for row in trend_result]
            }
        except Exception as e:
            logging.error(f"Error fetching trend data: {e}")
            error_message = (error_message + " | " if error_message else "") + "Could not fetch trend data."
            chart_data['trend'] = None  # Ensure key exists but is None on error

        # 2. Average Rate Trend (Last N, Filter Provider)
        try:
            logging.info(
                f"Fetching average pay rate trend for last {num_periods_to_show} periods (Provider: {selected_provider_id or 'All'}).")
            # ... Average rate trend query using selected_provider_id (same as before) ...
            avg_rate_query = text(f"""
                WITH RecentPeriods AS (
                    SELECT TOP {num_periods_to_show}
                        PP_ID,
                        FORMAT(PP_StartDate, 'MMM yyyy') as PeriodLabel
                    FROM tblPayPeriod
                    ORDER BY PP_StartDate DESC
                ), FilteredInvoices AS (
                    SELECT 
                        si.PayPeriodID,
                        si.TotalPay,
                        si.TotalHours,
                        si.ProviderID
                    FROM SessionInvoice si
                    JOIN tblPayRate pr ON si.ProviderID = pr.StaffID 
                        AND pr.StartDate <= (SELECT PP_StartDate FROM tblPayPeriod WHERE PP_ID = si.PayPeriodID)
                        AND ISNULL(pr.EndDate, (SELECT PP_EndDate FROM tblPayPeriod WHERE PP_ID = si.PayPeriodID)) >= (SELECT PP_EndDate FROM tblPayPeriod WHERE PP_ID = si.PayPeriodID)
                        AND pr.PayRateID = (SELECT TOP 1 sub_pr.PayRateID
                                            FROM tblPayRate sub_pr
                                            WHERE sub_pr.StaffID = si.ProviderID
                                            AND sub_pr.StartDate <= (SELECT PP_StartDate FROM tblPayPeriod WHERE PP_ID = si.PayPeriodID)
                                            AND ISNULL(sub_pr.EndDate, (SELECT PP_EndDate FROM tblPayPeriod WHERE PP_ID = si.PayPeriodID)) >= (SELECT PP_EndDate FROM tblPayPeriod WHERE PP_ID = si.PayPeriodID)
                                            ORDER BY sub_pr.StartDate DESC)
                    WHERE pr.PayRateType = 'H' 
                      AND si.TotalHours > 0 
                )
                SELECT
                    rp.PeriodLabel,
                    AVG(fi.TotalPay / fi.TotalHours) as AverageHourlyRate
                FROM RecentPeriods rp
                LEFT JOIN FilteredInvoices fi ON rp.PP_ID = fi.PayPeriodID
                                           AND (? IS NULL OR fi.ProviderID = ?) -- Apply provider filter here
                GROUP BY rp.PP_ID, rp.PeriodLabel
                ORDER BY rp.PP_ID ASC;
            """)
            avg_rate_params = (selected_provider_id, selected_provider_id)
            avg_rate_result = conn.execute(str(avg_rate_query), avg_rate_params).fetchall()
            logging.info(f"Fetched {len(avg_rate_result)} rows for average rate trend chart.")
            chart_data['average_rate_trend'] = {
                'labels': [row.PeriodLabel for row in avg_rate_result],
                'rates': [(float(row.AverageHourlyRate) if row.AverageHourlyRate is not None else 0) for row in
                          avg_rate_result]
            }
        except Exception as e:
            logging.error(f"Error fetching average pay rate trend data: {e}")
            error_message = (
                                error_message + " | " if error_message else "") + "Could not fetch average pay rate trend data."
            chart_data['average_rate_trend'] = None  # Ensure key exists but is None on error

        # --- Detail Charts for SELECTED Period (Apply Provider Filter) ---
        if selected_detail_period_id:  # Check if we have a period ID for details
            pp_timely_deadline = None
            try:
                period_dates_query = text("SELECT PP_Timely FROM tblPayPeriod WHERE PP_ID = ?")
                period_dates = conn.execute(str(period_dates_query), (selected_detail_period_id,)).fetchone()
                pp_timely_deadline = period_dates.PP_Timely if period_dates else None
                if not pp_timely_deadline:
                    logging.warning(
                        f"Could not find timely submission deadline for selected PayPeriodID {selected_detail_period_id}.")
                    # Don't set global error, just skip relevant chart
            except Exception as e:
                logging.error(f"Error fetching pay period details for period {selected_detail_period_id}: {e}")
                # Don't set global error, just skip relevant chart

            # 3. Paid vs Unpaid (Uses selected_detail_period_id & provider filter)
            try:
                logging.info(
                    f"Fetching paid/unpaid status for period {selected_detail_period_id} (Provider: {selected_provider_id or 'All'}).")
                # ... Query uses selected_detail_period_id ...
                paid_status_query = text("""
                    SELECT
                        CASE WHEN ISNULL(si.Paid, 0) = 1 THEN 'Paid' ELSE 'Unpaid' END as PaidStatus,
                        ISNULL(SUM(si.TotalPay), 0) as TotalAmount
                    FROM SessionInvoice si
                    WHERE si.PayPeriodID = ? 
                      AND (? IS NULL OR si.ProviderID = ?)
                    GROUP BY ISNULL(si.Paid, 0);
                """)
                paid_status_params = (selected_detail_period_id, selected_provider_id, selected_provider_id)
                paid_status_result = conn.execute(str(paid_status_query), paid_status_params).fetchall()
                logging.info(f"Fetched {len(paid_status_result)} rows for paid status chart.")
                chart_data['paid_status'] = {
                    'labels': [row.PaidStatus for row in paid_status_result],
                    'amounts': [float(row.TotalAmount) for row in paid_status_result]
                }
            except Exception as e:
                logging.error(f"Error fetching paid status data for period {selected_detail_period_id}: {e}")
                error_message = (error_message + " | " if error_message else "") + "Could not fetch paid status data."
                chart_data['paid_status'] = None

            # 4. Submission Status (Uses selected_detail_period_id, provider filter & deadline)
            if pp_timely_deadline:
                try:
                    logging.info(
                        f"Fetching submission status for period {selected_detail_period_id} (Provider: {selected_provider_id or 'All'}).")
                    # ... Query uses selected_detail_period_id ...
                    submission_status_query = f"""
                        SELECT 
                            CASE 
                                WHEN si.DateSubmitted <= pp.PP_Timely THEN 'Timely'
                                ELSE 'Late'
                            END as Status,
                            COUNT(DISTINCT si.InvoiceID) as Count
                        FROM SessionInvoice si
                        JOIN tblPayPeriod pp ON si.PayPeriodID = pp.PP_ID
                        WHERE si.PayPeriodID = ?
                        GROUP BY 
                            CASE 
                                WHEN si.DateSubmitted <= pp.PP_Timely THEN 'Timely'
                                ELSE 'Late'
                            END; 
                    """
                    submission_status_params = (selected_detail_period_id,)
                    cursor = conn.cursor()  # Create a cursor object
                    cursor.execute(submission_status_query, submission_status_params)
                    submission_status_result = cursor.fetchall()  # Store the result
                    logging.info(f"Fetched {len(submission_status_result)} rows for submission status chart.")
                    chart_data['submission_status'] = {
                        'labels': [row.Status for row in submission_status_result],
                        'amounts': [float(row.Count) for row in submission_status_result]
                    }
                except Exception as e:
                    logging.error(f"Error fetching submission status data for period {selected_detail_period_id}: {e}")
                    error_message = (
                                        error_message + " | " if error_message else "") + "Could not fetch submission status data."
                    chart_data['submission_status'] = None
            else:
                logging.warning(
                    f"Skipping submission status chart for period {selected_detail_period_id} due to missing deadline.")
                chart_data['submission_status'] = None  # Ensure key exists but is None

            # 5. Top N Providers by Pay (Uses selected_detail_period_id)
            try:
                logging.info(f"Fetching top {top_n_providers} providers by pay for period {selected_detail_period_id}.")
                # ... Query uses selected_detail_period_id ...
                top_prov_query = text(f"""
                    SELECT TOP {top_n_providers}
                        s.FullName as ProviderName,
                        ISNULL(SUM(si.TotalPay), 0) as TotalAmount
                    FROM SessionInvoice si
                    JOIN vwStaff s ON si.ProviderID = s.LoginID
                    WHERE si.PayPeriodID = ?
                    GROUP BY si.ProviderID, s.FullName
                    HAVING ISNULL(SUM(si.TotalPay), 0) > 0
                    ORDER BY TotalAmount DESC;
                """)
                top_prov_params = (selected_detail_period_id,)
                top_prov_result = conn.execute(str(top_prov_query), top_prov_params).fetchall()
                logging.info(f"Fetched {len(top_prov_result)} rows for top providers by pay chart.")
                chart_data['top_providers'] = {
                    'labels': [row.ProviderName for row in top_prov_result],
                    'amounts': [float(row.TotalAmount) for row in top_prov_result]
                }
            except Exception as e:
                logging.error(f"Error fetching top providers by pay data for period {selected_detail_period_id}: {e}")
                error_message = (
                                    error_message + " | " if error_message else "") + "Could not fetch top providers by pay data."
                chart_data['top_providers'] = None

            # 6. Top N Providers by Hours (Uses selected_detail_period_id)
            try:
                logging.info(
                    f"Fetching top {top_n_providers} providers by hours for period {selected_detail_period_id}.")
                # ... Query uses selected_detail_period_id ...
                top_hours_query = text(f"""
                    SELECT TOP {top_n_providers}
                        s.FullName as ProviderName,
                        ISNULL(SUM(si.TotalHours), 0) as TotalHours
                    FROM SessionInvoice si
                    JOIN vwStaff s ON si.ProviderID = s.LoginID
                    WHERE si.PayPeriodID = ?
                    GROUP BY si.ProviderID, s.FullName
                    HAVING ISNULL(SUM(si.TotalHours), 0) > 0
                    ORDER BY TotalHours DESC;
                """)
                top_hours_params = (selected_detail_period_id,)
                top_hours_result = conn.execute(str(top_hours_query), top_hours_params).fetchall()
                logging.info(f"Fetched {len(top_hours_result)} rows for top providers by hours chart.")
                chart_data['top_providers_hours'] = {
                    'labels': [row.ProviderName for row in top_hours_result],
                    'hours': [float(row.TotalHours) for row in top_hours_result]
                }
            except Exception as e:
                logging.error(f"Error fetching top providers by hours data for period {selected_detail_period_id}: {e}")
                error_message = (
                                    error_message + " | " if error_message else "") + "Could not fetch top providers by hours data."
                chart_data['top_providers_hours'] = None
        else:
            logging.warning("Skipping detail charts because no detail_period_id was determined.")
            error_message = (
                                error_message + " | " if error_message else "") + "Could not load detail charts as no pay period was specified or found."
            # Ensure detail chart keys exist but are None
            chart_data['paid_status'] = None
            chart_data['submission_status'] = None
            chart_data['top_providers'] = None
            chart_data['top_providers_hours'] = None

    except Exception as e:
        logging.error(f"An unexpected error occurred in payroll_dashboard route: {e}")
        error_message = "An unexpected error occurred while processing the request."
    finally:
        if conn:
            conn.close()
            logging.info("Database connection closed for payroll dashboard.")

    return render_template('payroll_dashboard.html',
                           chart_data=chart_data,
                           pay_periods=pay_periods_for_filter,  # Pass full list for dropdown
                           providers=providers_for_filter,
                           # Removed latest_period_info
                           selected_detail_period_id=selected_detail_period_id,  # Pass selected period ID
                           selected_provider_id=selected_provider_id,
                           error=error_message)

@payroll_bp.route('/report')
@login_required
@cache.cached(timeout=3600, key_prefix=make_cache_key)  # User-specific cache with 1-hour timeout
@check_permission('payroll.view')
def payroll_report():
    """Payroll reporting interface."""
    logging.info("Accessed /payroll-report route")

    # --- Get filter parameters from request ---
    try:
        selected_pay_period_id = request.args.get('pay_period_id', type=int)
        selected_provider_id = request.args.get('provider_id', default=None, type=int)  # Default to None for "All"
        selected_student_id = request.args.get('student_id', default=None, type=int)  # Default to None for "All"
        selected_submission_status = request.args.get('submission_status', default=None,
                                                      type=str)  # 'Timely', 'Late', None for all
        selected_paid_status = request.args.get('paid_status', default=None,
                                                type=int)  # 1 (Paid), 0 (Unpaid), None for all

        # Validate submission status input
        if selected_submission_status not in [None, '', 'Timely', 'Late']:
            logging.warning(f"Invalid submission_status '{selected_submission_status}', defaulting to None.")
            selected_submission_status = None  # Default to all if invalid value
        elif selected_submission_status == '':  # Explicitly handle empty string from form
            selected_submission_status = None

        # Validate paid status input
        if selected_paid_status not in [None, '', 0, 1]:
            logging.warning(f"Invalid paid_status '{selected_paid_status}', defaulting to None.")
            selected_paid_status = None  # Default to all if invalid value
        elif selected_paid_status == '':  # Handle empty string from form
            selected_paid_status = None

        logging.info(f"Filters received - PayPeriodID: {selected_pay_period_id}, ProviderID: {selected_provider_id}, "
                     f"StudentID: {selected_student_id}, SubmissionStatus: {selected_submission_status}, "
                     f"PaidStatus: {selected_paid_status}")

    except Exception as e:
        logging.error(f"Error parsing filter parameters: {e}")
        selected_pay_period_id = None
        selected_provider_id = None
        selected_student_id = None
        selected_submission_status = None
        selected_paid_status = None

    conn = None
    report_data = []
    pay_periods = []
    providers = []
    error_message = None

    try:
        conn = get_read_only_connection()
        logging.info("Database connection established for payroll report.")

        # --- Fetch data for filters ---
        try:
            pay_periods_query = text(
                "SELECT PP_ID, FORMAT(PP_StartDate, 'MMM dd yyyy') + ' - ' + FORMAT(PP_EndDate, 'MMM dd yyyy') AS PeriodRange FROM tblPayPeriod ORDER BY PP_StartDate DESC")
            pay_periods_sql = str(pay_periods_query)
            pay_periods_result = conn.execute(pay_periods_sql).fetchall()
            pay_periods = [(row[0], row[1]) for row in pay_periods_result]
            logging.info(f"Fetched {len(pay_periods)} pay periods for filter dropdown.")
            if not selected_pay_period_id and pay_periods:
                selected_pay_period_id = pay_periods[0][0]
                logging.info(f"Defaulting PayPeriodID to the most recent: {selected_pay_period_id}")
        except Exception as e:
            logging.error(f"Error fetching pay periods: {e}")
            error_message = "Could not fetch pay periods for filtering."

        try:
            providers_query = text(
                "SELECT LoginID, FullName FROM vwStaff WHERE isActive = 1 ORDER BY LastName, FirstName")
            providers_sql = str(providers_query)
            providers_result = conn.execute(providers_sql).fetchall()
            providers = [(row[0], row[1]) for row in providers_result]
            logging.info(f"Fetched {len(providers)} active providers for filter dropdown.")
        except Exception as e:
            logging.error(f"Error fetching providers: {e}")
            error_message = error_message + " Could not fetch providers for filtering." if error_message else "Could not fetch providers for filtering."

        # --- Fetch Report Data (only if a pay period is selected) ---
        if selected_pay_period_id:
            logging.info(f"Attempting to fetch report data for PayPeriodID: {selected_pay_period_id}")

            # Log the parameters being used for filtering
            logging.info(
                f"Report Query Filters - PayPeriodID: {selected_pay_period_id}, ProviderID: {selected_provider_id}, "
                f"StudentID: {selected_student_id}, SubmissionStatus: {selected_submission_status}, "
                f"PaidStatus: {selected_paid_status}")

            # --- Preliminary Count Query (Check base invoice existence) ---
            try:
                count_query_template = """
                    SELECT COUNT(DISTINCT si.InvoiceID)
                    FROM SessionInvoice si
                    INNER JOIN vwStaff staff ON si.ProviderID = staff.LoginID
                    INNER JOIN vwStudents students ON si.StudentID = students.StudentID
                    WHERE
                        si.PayPeriodID = ? -- Param 1: PayPeriodID
                        AND (? IS NULL OR si.ProviderID = ?) -- Param 2 & 3: ProviderID
                        AND (? IS NULL OR si.StudentID = ?);   -- Param 4 & 5: StudentID
                """
                count_params_tuple = (
                    selected_pay_period_id,
                    selected_provider_id,
                    selected_provider_id,
                    selected_student_id,
                    selected_student_id
                )
                logging.debug(f"Executing preliminary count query with params: {count_params_tuple}")
                # Fetch the first row and get the first column for the count
                count_row = conn.execute(count_query_template, count_params_tuple).fetchone()
                preliminary_count = count_row[
                    0] if count_row else 0  # Get count from first column, default to 0 if no row
                logging.info(
                    f"Preliminary count (before pay rate join & status filters): {preliminary_count} invoices found.")
            except Exception as count_e:
                logging.error(f"Error during preliminary count query: {count_e}")
                preliminary_count = -1  # Indicate error

            # --- Original Main Data Fetch Query --- 
            # Use the SQL query template with ? placeholders
            # REMOVED submission status logic from base template
            sql_query_template = """
                -- SQL Query based on sp_GetPayrollReportData logic using ? placeholders
                DECLARE @PP_StartDate DATETIME, @PP_EndDate DATETIME, @PP_Timely DATETIME;
                SELECT @PP_StartDate = PP_StartDate, @PP_EndDate = PP_EndDate, @PP_Timely = PP_Timely
                FROM tblPayPeriod WHERE PP_ID = ?; -- Placeholder 1: pay_period_id

                -- Find the latest pay rate active at the start of the pay period for each staff member
                WITH LatestPayRateForPeriod AS (
                    SELECT
                        pr.StaffID,
                        pr.PayRateID,
                        ROW_NUMBER() OVER (PARTITION BY pr.StaffID ORDER BY pr.StartDate DESC) as rn
                    FROM tblPayRate pr
                    WHERE
                        pr.StartDate <= @PP_StartDate
                        AND (pr.EndDate IS NULL OR pr.EndDate >= @PP_StartDate)
                )
                -- Main Query
                SELECT
                    si.ProviderID,
                    staff.FullName AS ProviderName,
                    staff.PayrollNum AS ProviderPayrollNum,
                    si.PayPeriodID,
                    FORMAT(@PP_StartDate, 'MMM dd yyyy') + ' - ' + FORMAT(@PP_EndDate, 'MMM dd yyyy') AS PayPeriodRange,
                    si.InvoiceID,
                    si.StudentID,
                    students.FullName AS StudentName,
                    si.DateSubmitted,
                    CASE WHEN si.DateSubmitted <= @PP_Timely THEN 'Timely' ELSE 'Late' END AS SubmissionStatus,
                    ISNULL(si.TotalHours, 0) AS TotalHours,
                    ISNULL(pr.Amount, 0) AS PayRateAmount,
                    pr.PayRateType AS PayRateTypeCode,
                    ISNULL(pr.Commission, 0) AS CommissionRate,
                    (ISNULL(si.TotalHours, 0) * ISNULL(pr.Amount, 0)) + (ISNULL(si.TotalHours, 0) * ISNULL(pr.Commission, 0)) AS CalculatedPay,
                    ISNULL(si.TotalPay, 0) AS SubmittedTotalPay,
                    ISNULL(si.Paid, 0) AS IsPaid,
                    si.PayDate
                FROM SessionInvoice si
                INNER JOIN vwStaff staff ON si.ProviderID = staff.LoginID
                INNER JOIN vwStudents students ON si.StudentID = students.StudentID
                LEFT JOIN LatestPayRateForPeriod lpr ON si.ProviderID = lpr.StaffID AND lpr.rn = 1
                LEFT JOIN tblPayRate pr ON lpr.PayRateID = pr.PayRateID
                WHERE
                    si.PayPeriodID = ? -- Placeholder 2: pay_period_id
                    AND (? IS NULL OR si.ProviderID = ?) -- Placeholders 3 & 4: provider_id
                    AND (? IS NULL OR si.StudentID = ?)    -- Placeholders 5 & 6: student_id
                    AND (? IS NULL OR ISNULL(si.Paid, 0) = ?) -- Placeholders 7 & 8: paid_status
            """

            # Parameters tuple (only 8 now)
            params_list = [
                selected_pay_period_id,  # 1
                selected_pay_period_id,  # 2
                selected_provider_id,  # 3
                selected_provider_id,  # 4
                selected_student_id,  # 5
                selected_student_id,  # 6
                selected_paid_status,  # 7
                selected_paid_status  # 8
            ]

            # Dynamically add submission status filter to the SQL string
            if selected_submission_status == 'Timely':
                sql_query_template += " AND si.DateSubmitted <= @PP_Timely"
                logging.debug("Added Timely submission filter.")
            elif selected_submission_status == 'Late':
                sql_query_template += " AND si.DateSubmitted > @PP_Timely"
                logging.debug("Added Late submission filter.")
            # No clause added if selected_submission_status is None or empty

            # Add ORDER BY clause
            sql_query_template += " ORDER BY staff.LastName, staff.FirstName, si.InvoiceID;"

            # Convert list to tuple for execution
            params_tuple = tuple(params_list)

            try:
                logging.debug(f"Executing SQL: {sql_query_template[:300]}... with params: {params_tuple}")
                result_cursor = conn.execute(sql_query_template, params_tuple)
                results = result_cursor.fetchall()

                column_names = [desc[0] for desc in result_cursor.description] if results else []
                report_data = [dict(zip(column_names, row)) for row in results]
                logging.info(f"Fetched {len(report_data)} rows for payroll report.")
            except Exception as e:
                logging.error(f"Error executing payroll report query: {e}")
                error_message = "An error occurred while fetching the report data."
        else:
            logging.info("No pay period selected, skipping report data fetch.")
            if not pay_periods:
                error_message = "No pay periods found in the system. Cannot generate report."
            else:
                error_message = "Please select a pay period to generate the report."

    except Exception as e:
        logging.error(f"An unexpected error occurred in payroll_report route: {e}")
        error_message = "An unexpected error occurred while processing the request."
    finally:
        if conn:
            conn.close()
            logging.info("Database connection closed for payroll report.")

    return render_template('payroll_report.html',
                           report_data=report_data,
                           pay_periods=pay_periods,
                           providers=providers,
                           selected_pay_period_id=selected_pay_period_id,
                           selected_provider_id=selected_provider_id,
                           selected_student_id=selected_student_id,
                           selected_submission_status=selected_submission_status,
                           selected_paid_status=selected_paid_status,
                           error=error_message) 