import logging
import time
import io
import csv
from datetime import datetime, timedelta
from contextlib import contextmanager

# Helper functions for safe type conversion
def safe_float(value, default=0.0):
    """Convert value to float safely, returning default if value is None."""
    return float(value) if value is not None else default

def safe_int(value, default=0):
    """Convert value to int safely, returning default if value is None."""
    return int(value) if value is not None else default

from flask import Blueprint, render_template, current_app, jsonify, request, session, redirect, url_for, Response, flash
from sqlalchemy import text

from app import cache
from app.auth import login_required
from app.database import get_read_only_connection
from app.middleware.permission_check import check_permission
from app import make_cache_key  # Import the custom cache key function

# Create the blueprint with a url_prefix
billing_bp = Blueprint('billing', __name__, url_prefix='/billing')

# Use the same context manager from routes.py for database connections
@contextmanager
def safe_db_connection(timeout=10, operation_name="database operation"):
    """
    Context manager to safely handle database connections with timeouts and proper cleanup.
    
    Args:
        timeout (int): Query timeout in seconds
        operation_name (str): Name of the operation for logging purposes
        
    Yields:
        Connection: The database connection object
    """
    conn = None
    start_time = time.time()
    
    try:
        # Get connection
        conn = get_read_only_connection()
        
        # Set timeout for queries (in seconds)
        conn.timeout = timeout
        
        # Use query hints for SQL Server
        conn.execute("SET NOCOUNT ON;")  # Reduces network traffic
        conn.execute("SET ARITHABORT ON;")  # Terminates queries when overflow/divide-by-zero occurs
        conn.execute("SET QUERY_GOVERNOR_COST_LIMIT 0;")  # Remove cost limits
        conn.execute(f"SET LOCK_TIMEOUT {timeout * 1000};")  # Set lock timeout in milliseconds
        
        connection_time = int((time.time() - start_time) * 1000)
        logging.debug(f"Database connection established in {connection_time}ms with {timeout}s timeout for {operation_name}")
        
        # Yield the connection for use in the with block
        yield conn
        
    except Exception as e:
        elapsed_time = int((time.time() - start_time) * 1000)
        error_msg = str(e)
        
        # Provide more helpful error messages based on SQL error codes
        if 'HYT00' in error_msg or 'timeout' in error_msg.lower():
            logging.error(f"Query timeout after {elapsed_time}ms during {operation_name} (timeout limit: {timeout}s): {error_msg}")
        elif '08S01' in error_msg:
            logging.error(f"Communication link failure after {elapsed_time}ms during {operation_name}: {error_msg}")
        elif '08001' in error_msg:
            logging.error(f"Connection failure after {elapsed_time}ms during {operation_name}: {error_msg}")
        elif '42000' in error_msg:
            logging.error(f"Syntax error after {elapsed_time}ms during {operation_name}: {error_msg}")
        else:
            logging.error(f"Database error after {elapsed_time}ms during {operation_name}: {error_msg}")
        
        raise
    finally:
        # Always ensure connection is closed
        if conn:
            try:
                conn.close()
                elapsed_time = int((time.time() - start_time) * 1000)
                logging.debug(f"Database connection closed after {elapsed_time}ms for {operation_name}")
            except Exception as close_error:
                logging.error(f"Error closing database connection after {operation_name}: {str(close_error)}")


@billing_bp.route('/dashboard')
@login_required
@cache.cached(timeout=3600, key_prefix=make_cache_key)  # User-specific cache with 1-hour timeout
@check_permission('report.view')
def billing_dashboard():
    """Interactive billing dashboard with visualizations of key billing metrics."""
    logging.info("Accessed /billing/dashboard route")
    
    # Get filter parameters
    time_period = request.args.get('time_period', 'last30days')
    entity_filter = request.args.get('entity', None, type=int)
    billable_status = request.args.get('billable_status', None)
    
    # Validate inputs
    if billable_status not in [None, '', 'Billable', 'Not Billable', 'Pending']:
        billable_status = None
    elif billable_status == '':
        billable_status = None
    
    # Calculate date ranges based on time_period
    end_date = datetime.now().date()
    if time_period == 'last7days':
        start_date = end_date - timedelta(days=7)
        period_name = "Last 7 Days"
    elif time_period == 'last30days':
        start_date = end_date - timedelta(days=30)
        period_name = "Last 30 Days"
    elif time_period == 'last90days':
        start_date = end_date - timedelta(days=90)
        period_name = "Last 90 Days"
    elif time_period == 'year':
        start_date = datetime(end_date.year, 1, 1).date()
        period_name = f"Year {end_date.year}"
    else:
        # Default to last 30 days
        start_date = end_date - timedelta(days=30)
        period_name = "Last 30 Days"
        time_period = 'last30days'
    
    # Format dates for SQL
    start_date_str = start_date.strftime('%Y-%m-%d')
    end_date_str = end_date.strftime('%Y-%m-%d')
    
    # Set query timeout - billing queries can be complex
    QUERY_TIMEOUT = 120  # seconds - increased from 90 to 120 seconds
    
    # Initialize variables
    dashboard_data = {}
    entities = []
    insurance_providers = []
    error_message = None
    
    # Number of retry attempts for database operations
    max_retries = 3
    retry_delay = 1  # seconds
    
    # Generate cache key for dashboard data
    dashboard_cache_key = f"billing_dashboard_{time_period}_{entity_filter}_{billable_status}"
    cached_dashboard = cache.get(dashboard_cache_key)
    if cached_dashboard:
        logging.info(f"Using cached dashboard data for {time_period}, entity={entity_filter}, status={billable_status}")
        dashboard_data = cached_dashboard
    
    try:
        # Use context manager to handle DB connection properly
        logging.info("Attempting to get SQL Server database connection...")
        
        # 1. First, fetch entities for the filter dropdown (with caching)
        entity_cache_key = f"top_entities_dropdown_data"
        entities = cache.get(entity_cache_key)
        if entities is None:
            for retry in range(max_retries):
                try:
                    with safe_db_connection(timeout=QUERY_TIMEOUT, operation_name=f"fetch_entities (attempt {retry+1}/{max_retries})") as conn:
                        entities_query = text("""
                            SELECT e_id, e_name 
                            FROM tblEntities WITH (NOLOCK)
                            ORDER BY e_name
                        """)
                        entities_sql = str(entities_query)
                        entities_result = conn.execute(entities_sql).fetchall()
                        entities = [(row.e_id, row.e_name) for row in entities_result]
                        # Cache entity data for 24 hours - they don't change often
                        cache.set(entity_cache_key, entities, timeout=86400)
                        logging.info(f"Fetched and cached {len(entities)} entities for filter dropdown")
                    # If successful, break out of the retry loop
                    break
                except Exception as e:
                    logging.error(f"Error on attempt {retry+1}/{max_retries} fetching entities: {e}")
                    if retry < max_retries - 1:
                        logging.info(f"Retrying in {retry_delay} seconds...")
                        time.sleep(retry_delay)
                        retry_delay *= 2  # Exponential backoff
                    else:
                        logging.error(f"Failed to fetch entities after {max_retries} attempts: {e}")
                        error_message = "Could not fetch entities for filtering."
                        entities = []  # Default to empty list
        else:
            logging.info("Using cached entities data for dropdown")
        
        # 1b. Fetch insurance providers for the filter dropdown
        insurance_cache_key = f"insurance_providers_dropdown_data"
        insurance_providers = cache.get(insurance_cache_key)
        if insurance_providers is None:
            for retry in range(max_retries):
                try:
                    with safe_db_connection(timeout=QUERY_TIMEOUT, operation_name=f"fetch_insurance (attempt {retry+1}/{max_retries})") as conn:
                        insurance_query = text("""
                            SELECT Ins_ID, Ins_Name 
                            FROM tblInsurances WITH (NOLOCK)
                            WHERE Ins_Active = 1
                            ORDER BY Ins_Name
                        """)
                        insurance_sql = str(insurance_query)
                        insurance_result = conn.execute(insurance_sql).fetchall()
                        insurance_providers = [(row.Ins_ID, row.Ins_Name) for row in insurance_result]
                        # Cache insurance data for 24 hours - they don't change often
                        cache.set(insurance_cache_key, insurance_providers, timeout=86400)
                        logging.info(f"Fetched and cached {len(insurance_providers)} insurance providers for filter")
                    # If successful, break out of the retry loop
                    break
                except Exception as e:
                    logging.error(f"Error on attempt {retry+1}/{max_retries} fetching insurance providers: {e}")
                    if retry < max_retries - 1:
                        logging.info(f"Retrying in {retry_delay} seconds...")
                        time.sleep(retry_delay)
                        retry_delay *= 2  # Exponential backoff
                    else:
                        logging.error(f"Failed to fetch insurance providers after {max_retries} attempts: {e}")
                        error_message = (error_message + " | " if error_message else "") + "Could not fetch insurance providers for filtering."
        else:
            logging.info(f"Using cached insurance providers data ({len(insurance_providers)} providers)")
        
        # Only fetch dashboard data if not cached
        if not cached_dashboard:
            # Check if all filters are available before fetching dashboard data
            if not error_message:
                # Only proceed with dashboard data if we have required components
                dashboard_data = fetch_dashboard_data(start_date_str, end_date_str, entity_filter, billable_status, QUERY_TIMEOUT)
                
                # If we got valid data, cache it
                if dashboard_data and not isinstance(dashboard_data, str):  # Ensure it's not an error string
                    cache.set(dashboard_cache_key, dashboard_data, timeout=1800)  # 30 minutes cache
                elif isinstance(dashboard_data, str):
                    # If we got an error string, set it as the error message
                    error_message = dashboard_data
                    dashboard_data = {}
            
    except Exception as e:
        logging.error(f"An unexpected error occurred in billing_dashboard route: {e}")
        error_message = "An unexpected error occurred while processing the billing dashboard request."
    
    # Render the dashboard template
    return render_template('billing_dashboard.html',
                           dashboard_data=dashboard_data,
                           entities=entities,
                           insurance_providers=insurance_providers,
                           time_period=time_period,
                           period_name=period_name,
                           selected_entity=entity_filter,
                           selected_billable_status=billable_status,
                           start_date=start_date.strftime('%m/%d/%Y'),
                           end_date=end_date.strftime('%m/%d/%Y'),
                           error=error_message)


def fetch_dashboard_data(start_date_str, end_date_str, entity_filter, billable_status, query_timeout):
    """
    Helper function to fetch dashboard data with retry logic.
    Returns dashboard data dict or error message string.
    """
    dashboard_data = {}
    max_retries = 3
    retry_delay = 1  # seconds
    
    # 2. Dashboard Widget 1: Billing Status Distribution
    try:
        for retry in range(max_retries):
            try:
                with safe_db_connection(timeout=query_timeout, operation_name=f"billing_status_chart (attempt {retry+1}/{max_retries})") as conn:
                    status_query = """
                        -- Heavily optimized query to prevent timeout
                        SELECT 
                            b.Status_name AS BillableStatusName,
                            COUNT(DISTINCT e.sessionid) AS SessionCount,
                            SUM(e.Units) AS TotalUnits,
                            SUM(e.Units * 15) AS TotalMinutes
                        FROM vwbillingscrubexport_gh e WITH (NOLOCK)
                        JOIN sess s WITH (NOLOCK) ON e.sessionid = s.sessid
                        JOIN sessionstatus b WITH (NOLOCK) ON e.BillableStatus = b.status_id
                        -- Remove Student join completely to improve performance
                        WHERE 
                            s.sessdate BETWEEN ? AND ?
                            AND (? IS NULL OR e.entityid = ?)
                            -- Removed filtering on Student for speed
                        GROUP BY 
                            b.Status_name
                        ORDER BY 
                            COUNT(DISTINCT e.sessionid) DESC
                        OPTION (MAXDOP 2)
                    """
                    
                    status_params = (start_date_str, end_date_str, entity_filter, entity_filter)
                    status_result = conn.execute(status_query, status_params).fetchall()
                    
                    dashboard_data['billing_status'] = {
                        'labels': [row.BillableStatusName for row in status_result],
                        'sessions': [row.SessionCount for row in status_result],
                        'units': [row.TotalUnits for row in status_result],
                        'minutes': [row.TotalMinutes for row in status_result]
                    }
                    logging.info(f"Fetched billing status distribution with {len(status_result)} statuses")
                break  # If successful, break out of retry loop
            except Exception as e:
                logging.error(f"Error on attempt {retry+1}/{max_retries} fetching billing status: {e}")
                if retry < max_retries - 1:
                    logging.info(f"Retrying in {retry_delay} seconds...")
                    time.sleep(retry_delay)
                    retry_delay *= 2  # Exponential backoff
                else:
                    raise  # Re-raise the last exception if all retries failed
    except Exception as e:
        logging.error(f"Error fetching billing status distribution: {e}")
        dashboard_data['billing_status'] = None
        return "Could not load billing status chart. Try a smaller date range or try again later."
    
    # 3. Dashboard Widget 2: Daily Billing Trend - SIMPLIFIED TO PREVENT TIMEOUT
    try:
        for retry in range(max_retries):
            try:
                with safe_db_connection(timeout=query_timeout, operation_name=f"billing_trend_chart (attempt {retry+1}/{max_retries})") as conn:
                    # Extremely simplified trend query - use monthly aggregation instead of weekly
                    trend_query = """
                        -- Further simplified monthly aggregation to prevent timeouts
                        SELECT 
                            DATEPART(YEAR, s.sessdate) AS Year,
                            DATEPART(MONTH, s.sessdate) AS Month,
                            FORMAT(DATEFROMPARTS(DATEPART(YEAR, s.sessdate), DATEPART(MONTH, s.sessdate), 1), 'MMM yyyy') AS FormattedDate,
                            COUNT(DISTINCT e.sessionid) AS SessionCount,
                            SUM(e.Units) AS TotalUnits,
                            SUM(e.Units * 15) AS TotalMinutes
                        FROM vwbillingscrubexport_gh e WITH (NOLOCK)
                        JOIN sess s WITH (NOLOCK) ON e.sessionid = s.sessid
                        WHERE 
                            s.sessdate BETWEEN ? AND ?
                            AND (? IS NULL OR e.entityid = ?)
                            AND (? IS NULL OR e.BillableStatus = (
                                SELECT TOP 1 status_id 
                                FROM sessionstatus WITH (NOLOCK)
                                WHERE Status_name = ?
                            ))
                        GROUP BY 
                            DATEPART(YEAR, s.sessdate),
                            DATEPART(MONTH, s.sessdate)
                        ORDER BY 
                            DATEPART(YEAR, s.sessdate),
                            DATEPART(MONTH, s.sessdate)
                        OPTION (MAXDOP 2)
                    """
                    
                    trend_params = (
                        start_date_str, end_date_str,      # Date range
                        entity_filter, entity_filter,      # Entity filter
                        billable_status, billable_status   # Status name for lookup
                    )
                    
                    trend_result = conn.execute(trend_query, trend_params).fetchall()
                    dashboard_data['daily_trend'] = {
                        'dates': [row.FormattedDate for row in trend_result],
                        'sessions': [row.SessionCount for row in trend_result],
                        'units': [row.TotalUnits for row in trend_result],
                        'minutes': [row.TotalMinutes for row in trend_result]
                    }
                    logging.info(f"Fetched daily billing trend with {len(trend_result)} days")
                break  # If successful, break out of retry loop
            except Exception as e:
                logging.error(f"Error on attempt {retry+1}/{max_retries} fetching daily trend: {e}")
                if retry < max_retries - 1:
                    logging.info(f"Retrying in {retry_delay} seconds...")
                    time.sleep(retry_delay)
                    retry_delay *= 2  # Exponential backoff
                else:
                    raise  # Re-raise the last exception if all retries failed
    except Exception as e:
        logging.error(f"Error fetching daily billing trend: {e}")
        dashboard_data['daily_trend'] = None
        # Don't return early, try to fetch the remaining components
    
    # 4. Dashboard Widget 3: Top Entities by Session Count
    try:
        for retry in range(max_retries):
            try:
                with safe_db_connection(timeout=query_timeout, operation_name=f"top_entities_mini_chart (attempt {retry+1}/{max_retries})") as conn:
                    top_entities_query = """
                        SELECT TOP 5  -- Reduced from 10 to 5 to minimize data
                            en.e_name AS EntityName,
                            COUNT(DISTINCT e.sessionid) AS SessionCount,
                            SUM(e.Units) AS TotalUnits,
                            SUM(e.Units * 15) AS TotalMinutes
                        FROM vwbillingscrubexport_gh e WITH (NOLOCK)
                        JOIN sess s WITH (NOLOCK) ON e.sessionid = s.sessid
                        JOIN tblentities en WITH (NOLOCK) ON e.entityid = en.e_id
                        -- Removed student join to improve performance
                        WHERE 
                            s.sessdate BETWEEN ? AND ?
                            AND (? IS NULL OR (
                                SELECT TOP 1 status_id 
                                FROM sessionstatus WITH (NOLOCK)
                                WHERE Status_name = ?
                            ) = e.BillableStatus)
                            -- Removed student filter to reduce query complexity
                        GROUP BY 
                            en.e_name
                        ORDER BY 
                            COUNT(DISTINCT e.sessionid) DESC
                        OPTION (MAXDOP 2)
                    """
                    
                    top_entities_params = (
                        start_date_str, end_date_str,
                        billable_status, billable_status
                    )
                    
                    top_entities_result = conn.execute(top_entities_query, top_entities_params).fetchall()
                    dashboard_data['top_entities'] = {
                        'names': [row.EntityName for row in top_entities_result],
                        'sessions': [row.SessionCount for row in top_entities_result],
                        'units': [row.TotalUnits for row in top_entities_result],
                        'minutes': [row.TotalMinutes for row in top_entities_result]
                    }
                    logging.info(f"Fetched top entities with {len(top_entities_result)} entities")
                break  # If successful, break out of retry loop
            except Exception as e:
                logging.error(f"Error on attempt {retry+1}/{max_retries} fetching top entities: {e}")
                if retry < max_retries - 1:
                    logging.info(f"Retrying in {retry_delay} seconds...")
                    time.sleep(retry_delay)
                    retry_delay *= 2  # Exponential backoff
                else:
                    raise  # Re-raise the last exception if all retries failed
    except Exception as e:
        logging.error(f"Error fetching top entities: {e}")
        dashboard_data['top_entities'] = None
        # Continue with remaining components
    
    # 5. Dashboard Widget 4: Top Providers by Session Count
    try:
        for retry in range(max_retries):
            try:
                with safe_db_connection(timeout=query_timeout, operation_name=f"top_providers_chart (attempt {retry+1}/{max_retries})") as conn:
                    # Completely redesigned query to avoid column name issues
                    top_providers_query = """
                        SELECT TOP 5
                            e.BillingProvider AS ProviderName,  -- Use BillingProvider field directly from the view
                            COUNT(DISTINCT e.sessionid) AS SessionCount,
                            SUM(e.Units) AS TotalUnits,
                            SUM(e.Units * 15) AS TotalMinutes
                        FROM vwbillingscrubexport_gh e WITH (NOLOCK)
                        JOIN sess s WITH (NOLOCK) ON e.sessionid = s.sessid
                        WHERE 
                            s.sessdate BETWEEN ? AND ?
                            AND (? IS NULL OR e.entityid = ?)
                            AND (? IS NULL OR (
                                SELECT TOP 1 status_id 
                                FROM sessionstatus WITH (NOLOCK)
                                WHERE Status_name = ?
                            ) = e.BillableStatus)
                            -- Removed student filters to improve performance
                        GROUP BY 
                            e.BillingProvider  -- Group by the field we're selecting
                        ORDER BY 
                            COUNT(DISTINCT e.sessionid) DESC
                        OPTION (MAXDOP 2)
                    """
                    
                    top_providers_params = (
                        start_date_str, end_date_str,
                        entity_filter, entity_filter,
                        billable_status, billable_status
                    )
                    
                    top_providers_result = conn.execute(top_providers_query, top_providers_params).fetchall()
                    dashboard_data['top_providers'] = {
                        'names': [row.ProviderName for row in top_providers_result],
                        'sessions': [row.SessionCount for row in top_providers_result],
                        'units': [row.TotalUnits for row in top_providers_result],
                        'minutes': [row.TotalMinutes for row in top_providers_result]
                    }
                    logging.info(f"Fetched top providers with {len(top_providers_result)} providers")
                break  # If successful, break out of retry loop
            except Exception as e:
                logging.error(f"Error on attempt {retry+1}/{max_retries} fetching top providers: {e}")
                if retry < max_retries - 1:
                    logging.info(f"Retrying in {retry_delay} seconds...")
                    time.sleep(retry_delay)
                    retry_delay *= 2  # Exponential backoff
                else:
                    raise  # Re-raise the last exception if all retries failed
    except Exception as e:
        logging.error(f"Error fetching top providers: {e}")
        dashboard_data['top_providers'] = None
        # Continue with remaining components
    
    # 6. Dashboard Widget 5: Billing Summary
    try:
        for retry in range(max_retries):
            try:
                with safe_db_connection(timeout=query_timeout, operation_name=f"billing_summary (attempt {retry+1}/{max_retries})") as conn:
                    # Completely simplified summary query to avoid timeout and column name issues
                    simplified_query = """
                        SELECT 
                            COUNT(DISTINCT e.sessionid) AS TotalSessions,
                            COUNT(DISTINCT s.studentid) AS TotalClients,
                            COUNT(DISTINCT e.BillingProvider) AS TotalProviders,  -- Count distinct provider names from the view
                            SUM(e.Units) AS TotalUnits,
                            SUM(e.Units * 15) AS TotalMinutes,
                            CASE WHEN COUNT(DISTINCT e.sessionid) > 0 
                                THEN SUM(e.Units * 15) / COUNT(DISTINCT e.sessionid)
                                ELSE 0 
                            END AS AvgSessionMinutes
                        FROM vwbillingscrubexport_gh e WITH (NOLOCK)
                        JOIN sess s WITH (NOLOCK) ON e.sessionid = s.sessid
                        -- Removed Student join for performance
                        WHERE 
                            s.sessdate BETWEEN ? AND ?
                            AND (? IS NULL OR e.entityid = ?)
                            AND (? IS NULL OR e.BillableStatus = (
                                SELECT TOP 1 status_id 
                                FROM sessionstatus WITH (NOLOCK)
                                WHERE Status_name = ?
                            ))
                        OPTION (MAXDOP 2)
                    """
                    
                    summary_params = (
                        start_date_str, end_date_str,
                        entity_filter, entity_filter,
                        billable_status, billable_status
                    )
                    
                    # Get all summary data in a single query
                    summary_result = conn.execute(simplified_query, summary_params).fetchone()
                    
                    if summary_result:
                        dashboard_data['summary'] = {
                            'total_sessions': summary_result.TotalSessions,
                            'total_clients': summary_result.TotalClients,
                            'total_providers': summary_result.TotalProviders,
                            'total_units': summary_result.TotalUnits,
                            'total_minutes': summary_result.TotalMinutes,
                            'avg_session_minutes': round(summary_result.AvgSessionMinutes or 0, 1)
                        }
                    else:
                        dashboard_data['summary'] = {
                            'total_sessions': 0,
                            'total_clients': 0,
                            'total_providers': 0,
                            'total_units': 0,
                            'total_minutes': 0,
                            'avg_session_minutes': 0
                        }
                    logging.info(f"Fetched billing summary data")
                break  # If successful, break out of retry loop
            except Exception as e:
                logging.error(f"Error on attempt {retry+1}/{max_retries} fetching billing summary: {e}")
                if retry < max_retries - 1:
                    logging.info(f"Retrying in {retry_delay} seconds...")
                    time.sleep(retry_delay)
                    retry_delay *= 2  # Exponential backoff
                else:
                    raise  # Re-raise the last exception if all retries failed
    except Exception as e:
        logging.error(f"Error fetching billing summary: {e}")
        dashboard_data['summary'] = None
        # Continue returning what we have
    
    return dashboard_data


@billing_bp.route('/stats')
@login_required
@cache.cached(timeout=3600, key_prefix=make_cache_key)  # User-specific cache with 1-hour timeout
@check_permission('report.view')
def billing_stats():
    # If it's an AJAX request or a regular GET with dateRange parameter, process the data
    if request.headers.get('X-Requested-With') == 'XMLHttpRequest' or request.args.get('dateRange'):
        date_range = request.args.get('dateRange', '')

        if not date_range:
            if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                return jsonify({'error': 'Date range is required'}), 400
            else:
                flash('Please select a date range.', 'warning')
                return render_template('billing_stats.html',
                                    total_sessions=0,
                                    total_billed=0,
                                    unique_clients=0,
                                    avg_session_duration=0,
                                    date_range='')

        try:
            start_date, end_date = date_range.split(' - ')
            start_date = datetime.strptime(start_date, '%m/%d/%Y')
            end_date = datetime.strptime(end_date, '%m/%d/%Y')
            
            # Limit date range to prevent timeouts - max 90 days
            date_diff = (end_date - start_date).days
            if date_diff > 90:
                message = "Date range exceeds 90 days. Please select a shorter period to prevent timeouts."
                if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                    return jsonify({'error': message}), 400
                else:
                    flash(message, 'warning')
                    return render_template('billing_stats.html',
                                        total_sessions=0,
                                        total_billed=0,
                                        unique_clients=0,
                                        avg_session_duration=0,
                                        date_range=date_range)
        except ValueError:
            if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                return jsonify({'error': 'Invalid date format'}), 400
            else:
                flash('Invalid date format. Please select a valid date range.', 'warning')
                return render_template('billing_stats.html',
                                    total_sessions=0,
                                    total_billed=0,
                                    unique_clients=0,
                                    avg_session_duration=0,
                                    date_range='')

        # Format dates for SQL query
        start_date_str = start_date.strftime('%Y-%m-%d')
        end_date_str = end_date.strftime('%Y-%m-%d')

        # Set shorter timeout for billing summary query since it's simpler now
        BILLING_QUERY_TIMEOUT = 30  # Increased to 30 seconds max for billing summary stats
        
        # Generate a cache key based on query parameters
        cache_key = f"billing_stats_{date_range}"
        cached_result = cache.get(cache_key)
        
        if cached_result:
            logging.info(f"Using cached billing stats for date range {date_range}")
            
            if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                return jsonify(cached_result)
            else:
                # For regular form submissions, render the template with the cached data
                return render_template('billing_stats.html',
                                    total_sessions=cached_result.get('total_sessions', 0),
                                    total_billed=cached_result.get('total_billed', 0),
                                    unique_clients=cached_result.get('unique_clients', 0),
                                    avg_session_duration=cached_result.get('avg_session_duration', 0),
                                    date_range=cached_result.get('date_range', date_range))
        
        # Simplified query to get only billing statistics summary - further optimized
        stats_query = """
        SELECT 
            COUNT(DISTINCT s.sessid) as total_sessions,
            COUNT(DISTINCT s.studentid) as unique_clients,
            0.0 as total_billed, -- No billing rates yet, set to zero
            AVG(CAST(e.Units * 15 as FLOAT)) as avg_duration
        FROM sess s WITH (NOLOCK)
        JOIN vwbillingscrubexport_gh e WITH (NOLOCK) ON s.sessid = e.sessionid
        JOIN Student st WITH (NOLOCK) ON s.studentid = st.StudentID
        WHERE 
            s.sessdate BETWEEN ? AND ?
            AND s.studentid <> 19790  -- Exclude test student
            AND LOWER(st.FirstName) NOT LIKE '%test%'
            AND LOWER(st.LastName) NOT LIKE '%test%'
            AND LOWER(st.FirstName) NOT LIKE '%sample%'
            AND LOWER(st.LastName) NOT LIKE '%sample%'
        OPTION (MAXDOP 2, OPTIMIZE FOR UNKNOWN)
        """

        # Number of retry attempts for database connections
        max_retries = 3
        retry_delay = 1  # seconds
        
        # Use our context manager to ensure connection is always closed
        for retry in range(max_retries):
            try:
                with safe_db_connection(timeout=BILLING_QUERY_TIMEOUT, operation_name=f"billing_stats (attempt {retry+1}/{max_retries})") as conn:
                    # Execute the query with parameters
                    query_start_time = time.time()
                    result = conn.execute(stats_query, (start_date_str, end_date_str)).fetchone()
                    query_duration_ms = int((time.time() - query_start_time) * 1000)
                    logging.info(f"Billing stats query completed in {query_duration_ms}ms for date range {date_range}")

                    if not result:
                        response_data = {
                            'total_sessions': 0,
                            'total_billed': 0,
                            'unique_clients': 0,
                            'avg_session_duration': 0,
                            'date_range': f"{start_date.strftime('%m/%d/%Y')} - {end_date.strftime('%m/%d/%Y')}",
                            'query_time_ms': query_duration_ms
                        }
                    else:
                        # Prepare response data
                        response_data = {
                            'total_sessions': safe_int(result.total_sessions),
                            'total_billed': safe_float(result.total_billed),
                            'unique_clients': safe_int(result.unique_clients),
                            'avg_session_duration': round(safe_float(result.avg_duration)),
                            'date_range': f"{start_date.strftime('%m/%d/%Y')} - {end_date.strftime('%m/%d/%Y')}",
                            'query_time_ms': query_duration_ms
                        }
                    
                    # Cache the result for longer - extended to 3 hours
                    cache.set(cache_key, response_data, timeout=10800)  # 3 hours
                    
                    if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                        return jsonify(response_data)
                    else:
                        # For regular form submissions, render the template with the data
                        return render_template('billing_stats.html',
                                            total_sessions=response_data.get('total_sessions', 0),
                                            total_billed=response_data.get('total_billed', 0),
                                            unique_clients=response_data.get('unique_clients', 0),
                                            avg_session_duration=response_data.get('avg_session_duration', 0),
                                            date_range=response_data.get('date_range', date_range))
                
                # If we reached here, the query was successful
                break
            except Exception as e:
                logging.error(f"Error on attempt {retry+1}/{max_retries} loading billing stats: {str(e)}")
                if retry < max_retries - 1:
                    logging.info(f"Retrying in {retry_delay} seconds...")
                    time.sleep(retry_delay)
                    retry_delay *= 2  # Exponential backoff
                else:
                    import traceback
                    error_msg = f"Failed to load billing statistics after {max_retries} attempts: {str(e)}\n{traceback.format_exc()}"
                    logging.error(error_msg)
                    
                    if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                        return jsonify({
                            'error': 'Database query timeout. Please try a smaller date range or try again later.',
                            'details': str(e) if not str(e).startswith('08') else 'Connection timed out'
                        }), 500
                    else:
                        flash('Database query timeout. Please try a smaller date range or try again later.', 'danger')
                        return render_template('billing_stats.html',
                                            total_sessions=0,
                                            total_billed=0,
                                            unique_clients=0,
                                            avg_session_duration=0,
                                            date_range=date_range)

    # For regular page load without parameters, just render the template with empty data
    return render_template('billing_stats.html',
                           total_sessions=0,
                           total_billed=0,
                           unique_clients=0,
                           avg_session_duration=0,
                           date_range='')


@billing_bp.route('/top_entities')
@login_required
@cache.cached(timeout=1800, key_prefix=make_cache_key)  # User-specific cache with 30-minute timeout
@check_permission('report.view')
def top_entities():
    """Display top entities by session count in a dedicated chart view."""
    logging.info("Accessed /billing/top_entities route")
    
    # Get filter parameters
    time_period = request.args.get('time_period', 'last30days')
    billable_status = request.args.get('billable_status', None)
    
    # Validate inputs
    if billable_status not in [None, '', 'Billable', 'Not Billable', 'Pending']:
        billable_status = None
    elif billable_status == '':
        billable_status = None
    
    # Calculate date ranges based on time_period
    end_date = datetime.now().date()
    if time_period == 'last7days':
        start_date = end_date - timedelta(days=7)
        period_name = "Last 7 Days"
    elif time_period == 'last30days':
        start_date = end_date - timedelta(days=30)
        period_name = "Last 30 Days"
    elif time_period == 'last90days':
        start_date = end_date - timedelta(days=90)
        period_name = "Last 90 Days"
    elif time_period == 'year':
        start_date = datetime(end_date.year, 1, 1).date()
        period_name = f"Year {end_date.year}"
    else:
        # Default to last 30 days
        start_date = end_date - timedelta(days=30)
        period_name = "Last 30 Days"
        time_period = 'last30days'
    
    # Format dates for SQL
    start_date_str = start_date.strftime('%Y-%m-%d')
    end_date_str = end_date.strftime('%Y-%m-%d')
    
    # Increase query timeout - this query can take longer
    QUERY_TIMEOUT = 180  # seconds - increased to 3 minutes
    
    # Initialize variables
    chart_data = {}
    entities = []
    error_message = None
    
    # Generate a cache key for entity data - can be cached longer since it rarely changes
    entity_cache_key = f"top_entities_dropdown_data"
    
    try:
        # Use context manager to handle DB connection properly
        with safe_db_connection(timeout=QUERY_TIMEOUT, operation_name="top_entities_chart") as conn:
            # 1. First, fetch entities for the filter dropdown (with caching)
            entities = cache.get(entity_cache_key)
            if entities is None:
                try:
                    entities_query = text("""
                        SELECT e_id, e_name 
                        FROM tblEntities WITH (NOLOCK)
                        ORDER BY e_name
                    """)
                    entities_sql = str(entities_query)
                    entities_result = conn.execute(entities_sql).fetchall()
                    entities = [(row.e_id, row.e_name) for row in entities_result]
                    # Cache entity data for 24 hours - they don't change often
                    cache.set(entity_cache_key, entities, timeout=86400)
                    logging.info(f"Fetched and cached {len(entities)} entities for filter dropdown")
                except Exception as e:
                    logging.error(f"Error fetching entities: {e}")
                    error_message = "Could not fetch entities for filtering."
                    entities = []  # Default to empty list
            else:
                logging.info("Using cached entities data for dropdown")
            
            # 2. Fetch data for Top Entities chart - OPTIMIZED QUERY
            try:
                # Pre-calculate status ID to avoid subquery for each row
                status_id = None
                if billable_status:
                    status_query = """
                        SELECT TOP 1 status_id 
                        FROM sessionstatus WITH (NOLOCK)
                        WHERE Status_name = ?
                    """
                    status_result = conn.execute(status_query, (billable_status,)).fetchone()
                    if status_result:
                        status_id = status_result.status_id
                
                # Generate a cache key based on filters
                chart_cache_key = f"top_entities_chart_data_{time_period}_{status_id}"
                chart_data = cache.get(chart_cache_key)
                
                if chart_data is None:
                    # Get sort parameters from request
                    sort_column = request.args.get('sort', 'LastName')  # Default sort by last name
                    sort_dir = request.args.get('dir', 'asc')  # Default ascending
                    
                    # Validate sort parameters
                    valid_sort_columns = {
                        'LastName': 's.LastName',
                        'FirstName': 's.FirstName',
                        'Role': 'r.R_Name',
                        'Location': 'ol.OfficeLocName',
                        'AverageWeeklyHours': 'AverageWeeklyHours',
                        'WeeklyPotentialHours': 'WeeklyPotentialHours',
                        'AvgWeeklyUnusedHours': 'AvgWeeklyUnusedHours',
                        'WeeksWorked': 'WeeksWorked'
                    }
                    
                    # Default to LastName if invalid sort column
                    sort_column = valid_sort_columns.get(sort_column, 's.LastName')
                    sort_dir = 'DESC' if sort_dir.lower() == 'desc' else 'ASC'
                    
                    # Adapted SQL query with proper parameterization and sorting
                    utilization_query = text("""
                        SET DATEFIRST 7;
                        
                        WITH LatestStaffIntake AS (
                            SELECT 
                                SI_LoginID,
                                CorporateAccount,
                                DATETERMINATED,
                                ROW_NUMBER() OVER (PARTITION BY SI_LoginID ORDER BY DateModified DESC) as intake_rn
                            FROM StaffIntake WITH (NOLOCK)
                            WHERE ISNULL(DATETERMINATED,'') = ''
                        ),
                        WeekNumbers AS (
                            -- Generate a row for each week in the date range
                            SELECT 
                                DATEADD(DAY, number * 7, ?) as WeekStart,
                                DATEADD(DAY, (number * 7) + 6, ?) as WeekEnd
                            FROM master.dbo.spt_values
                            WHERE type = 'P' 
                                AND number <= DATEDIFF(WEEK, ?, ?)
                        ),
                        ActualHours AS (
                            SELECT
                                s.TherapistID,
                                s.StudentID,
                                s.CodeID,
                                SUM(CAST(DATEDIFF(MINUTE, s.starttime, s.endtime) AS FLOAT) / 60.0) AS TotalActualHours,
                                COUNT(DISTINCT CONCAT(DATEPART(YEAR, s.sessdate), '-', FORMAT(DATEPART(ISO_WEEK, s.sessdate), '00'))) AS WeeksWorked
                            FROM sess s WITH (NOLOCK)
                            WHERE s.sessdate BETWEEN ? AND ?
                                AND s.SessionStatus in (4,16)  -- Only count completed/billed sessions
                            GROUP BY s.TherapistID, s.StudentID, s.CodeID
                        ),
                        PotentialHours AS (
                            SELECT 
                                tsl.TherapistID,
                                tsl.StudentID,
                                tsl.CodeID,
                                c.Code_Name,
                                -- Calculate total potential hours for the entire period
                                SUM(tsl.Units * c.MinutesPerUnit / 60.0) AS TotalPotentialHours,
                                -- Count number of weeks this assignment spans
                                COUNT(DISTINCT CONCAT(DATEPART(YEAR, w.WeekStart), '-', FORMAT(DATEPART(ISO_WEEK, w.WeekStart), '00'))) AS WeeksAssigned
                            FROM TherapistStudentLink tsl WITH (NOLOCK)
                            JOIN tblCodes c WITH (NOLOCK) ON c.Code_ID = tsl.CodeID
                            CROSS JOIN WeekNumbers w
                            WHERE tsl.startDate <= w.WeekEnd 
                                AND tsl.endDate >= w.WeekStart
                            GROUP BY tsl.TherapistID, tsl.StudentID, tsl.CodeID, c.Code_Name
                        )
                        SELECT DISTINCT 
                            S.LoginID,
                            s.FirstName,
                            s.LastName,
                            r.R_Name as Role,
                            r.R_ID as RoleID,
                            ol.OfficeLocName as Location,
                            ol.OfficeLocID as LocationID,
                            -- Actual hours worked
                            COALESCE(SUM(ah.TotalActualHours), 0) as TotalHours,
                            -- Average weekly hours actually worked
                            CASE 
                                WHEN MAX(ah.WeeksWorked) > 0 
                                THEN COALESCE(SUM(ah.TotalActualHours) / MAX(ah.WeeksWorked), 0)
                                ELSE 0 
                            END AS AverageWeeklyHours,
                            -- Weekly potential hours (total potential divided by weeks assigned)
                            CASE 
                                WHEN MAX(ph.WeeksAssigned) > 0 
                                THEN COALESCE(SUM(ph.TotalPotentialHours) / MAX(ph.WeeksAssigned), 0)
                                ELSE 0 
                            END as WeeklyPotentialHours,
                            -- Calculate unused capacity (potential - actual average)
                            CASE 
                                WHEN MAX(ph.WeeksAssigned) > 0 
                                THEN COALESCE((SUM(ph.TotalPotentialHours) / MAX(ph.WeeksAssigned)) - 
                                    (CASE 
                                        WHEN MAX(ah.WeeksWorked) > 0 
                                        THEN SUM(ah.TotalActualHours) / MAX(ah.WeeksWorked)
                                        ELSE 0 
                                    END), 0)
                                ELSE 0
                            END as AvgWeeklyUnusedHours,
                            MAX(ah.WeeksWorked) as WeeksWorked
                        FROM staff s WITH (NOLOCK)
                        INNER JOIN tblPayRate pr WITH (NOLOCK) ON s.LoginID = pr.StaffID
                        INNER JOIN tblroleofficelink rol WITH (NOLOCK) ON s.loginid = rol.loginid 
                        INNER JOIN tblOfficeLocations ol WITH (NOLOCK) ON rol.OfficeLocID = ol.OfficeLocID
                        INNER JOIN Roles r WITH (NOLOCK) ON rol.R_ID = r.R_ID
                        LEFT JOIN LatestStaffIntake si ON s.LoginID = si.SI_LoginID AND si.intake_rn = 1
                        LEFT JOIN PotentialHours ph ON s.LoginID = ph.TherapistID
                        LEFT JOIN ActualHours ah ON s.LoginID = ah.TherapistID 
                            AND ph.StudentID = ah.StudentID 
                            AND ph.CodeID = ah.CodeID
                        WHERE r.R_ID in (4,6)  -- BCBA and BT roles
                            AND (? IS NULL OR r.R_ID = ?)
                            AND (? IS NULL OR ol.OfficeLocID = ?)
                            AND GETDATE() BETWEEN pr.StartDate AND pr.EndDate
                            AND s.firstname NOT LIKE '%Test%' 
                            AND s.lastname NOT LIKE '%Test%'
                            -- Add filter to only include providers with assignments
                            AND EXISTS (
                                SELECT 1 
                                FROM TherapistStudentLink tsl WITH (NOLOCK)
                                WHERE tsl.TherapistID = s.LoginID
                                AND tsl.endDate >= ?  -- Current assignment or ended after start date
                                AND tsl.startDate <= ?  -- Started before or during the period
                                AND tsl.Units > 0  -- Has actual units assigned
                            )
                        GROUP BY 
                            S.LoginID, s.FirstName, s.LastName, r.R_Name, r.R_ID,
                            ol.OfficeLocName, ol.OfficeLocID
                        ORDER BY """ + sort_column + " " + sort_dir + """
                        OPTION (MAXDOP 2)
                    """)
                    
                    params = (
                        start_date, start_date,  # WeekNumbers start parameters
                        start_date, end_date,    # WeekNumbers date range for DATEDIFF
                        start_date, end_date,    # For ActualHours date range
                        role_filter, role_filter,  # Role filter
                        location_filter, location_filter  # Location filter
                    )
                    
                    result = conn.execute(str(utilization_query), params).fetchall()
                    
                    # Convert to list of dictionaries
                    provider_data = []
                    for row in result:
                        provider_data.append({
                            'LoginID': row.LoginID,
                            'FirstName': row.FirstName,
                            'LastName': row.LastName,
                            'Role': row.Role,
                            'RoleID': row.RoleID,
                            'Location': row.Location,
                            'LocationID': row.LocationID,
                            'TotalHours': float(row.TotalHours) if row.TotalHours else 0.0,
                            'AverageWeeklyHours': float(row.AverageWeeklyHours) if row.AverageWeeklyHours else 0.0,
                            'WeeklyPotentialHours': float(row.WeeklyPotentialHours) if row.WeeklyPotentialHours else 0.0,
                            'AvgWeeklyUnusedHours': float(row.AvgWeeklyUnusedHours) if row.AvgWeeklyUnusedHours else 0.0,
                            'WeeksWorked': row.WeeksWorked if row.WeeksWorked else 0
                        })
                    
                    # Cache the data for 1 hour
                    cache.set(chart_cache_key, provider_data, timeout=3600)
                    
                    logging.info(f"Fetched provider utilization data: {len(provider_data)} providers")
                    
            except Exception as e:
                logging.error(f"Error fetching top entities: {e}")
                chart_data = None
                error_message = "Could not load top entities chart due to database timeout. Try a narrower date range (e.g., Last 7 Days) or contact support."
            
    except Exception as e:
        logging.error(f"An unexpected error occurred in top_entities route: {e}")
        error_message = "An unexpected error occurred while processing the top entities request."
    
    # Render the template
    return render_template('top_entities.html',
                           chart_data=chart_data,
                           entities=entities,
                           time_period=time_period,
                           period_name=period_name,
                           selected_billable_status=billable_status,
                           start_date=start_date.strftime('%m/%d/%Y'),
                           end_date=end_date.strftime('%m/%d/%Y'),
                           error=error_message,
                           cache_notice="Data refreshes every hour") 


@billing_bp.route('/details')
@login_required
@cache.cached(timeout=3600, key_prefix=make_cache_key)  # User-specific cache with 1-hour timeout
@check_permission('report.view')
def billing_details():
    """Display detailed billing records in a dedicated table view."""
    logging.info("Accessed /billing/details route")
    
    # If it's an AJAX request or a regular GET with dateRange parameter, process the data
    if request.headers.get('X-Requested-With') == 'XMLHttpRequest' or request.args.get('dateRange'):
        date_range = request.args.get('dateRange', '')
        page = int(request.args.get('page', 1))
        page_size = int(request.args.get('pageSize', 50))
        offset = (page - 1) * page_size

        if not date_range:
            if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                return jsonify({'error': 'Date range is required'}), 400
            else:
                # For regular form submissions, just render the template
                flash('Please select a date range', 'warning')
                return render_template('billing_details.html',
                                      billing_data=[],
                                      total_sessions=0,
                                      total_billed=0,
                                      unique_clients=0,
                                      avg_session_duration=0,
                                      date_range='')

        try:
            start_date, end_date = date_range.split(' - ')
            start_date = datetime.strptime(start_date, '%m/%d/%Y')
            end_date = datetime.strptime(end_date, '%m/%d/%Y')
            
            # Limit date range to prevent timeouts - max 90 days
            date_diff = (end_date - start_date).days
            if date_diff > 90:
                message = "Date range exceeds 90 days. Please select a shorter period to prevent timeouts."
                if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                    return jsonify({'error': message}), 400
                else:
                    flash(message, 'warning')
                    return render_template('billing_details.html',
                                        billing_data=[],
                                        total_sessions=0,
                                        total_billed=0,
                                        unique_clients=0,
                                        avg_session_duration=0,
                                        date_range='')
        except ValueError:
            if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                return jsonify({'error': 'Invalid date format'}), 400
            else:
                flash('Invalid date format. Please select a valid date range.', 'warning')
                return render_template('billing_details.html',
                                      billing_data=[],
                                      total_sessions=0,
                                      total_billed=0,
                                      unique_clients=0,
                                      avg_session_duration=0,
                                      date_range='')

        # Format dates for SQL query
        start_date_str = start_date.strftime('%Y-%m-%d')
        end_date_str = end_date.strftime('%Y-%m-%d')

        # Set longer timeout for billing details query as it's complex - increased substantially
        BILLING_QUERY_TIMEOUT = 60  # Increased from 30 to 60 seconds
        
        # Generate a cache key based on query parameters
        cache_key = f"billing_details_{date_range}_{page}_{page_size}"
        cached_result = cache.get(cache_key)
        
        if cached_result:
            logging.info(f"Using cached billing details for date range {date_range}, page {page}")
            
            if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                return jsonify(cached_result)
            else:
                # For regular form submissions, render the template with the cached data
                return render_template('billing_details.html',
                                      billing_data=cached_result.get('billing_data', []),
                                      total_sessions=cached_result.get('total_sessions', 0),
                                      total_billed=cached_result.get('total_billed', 0),
                                      unique_clients=cached_result.get('unique_clients', 0),
                                      avg_session_duration=cached_result.get('avg_session_duration', 0),
                                      date_range=cached_result.get('date_range', date_range))
        
        # HEAVILY OPTIMIZED query - Split into two separate queries to improve performance
        # First query gets aggregated statistics only
        stats_query = """
        SELECT 
            COUNT(DISTINCT s.sessid) as total_sessions,
            COUNT(DISTINCT s.studentid) as unique_clients,
            0.0 as total_billed, -- No billing rates yet, set to zero
            AVG(CAST(e.Units * 15 as FLOAT)) as avg_duration
        FROM sess s WITH (NOLOCK)
        JOIN vwbillingscrubexport_gh e WITH (NOLOCK) ON s.sessid = e.sessionid
        JOIN Student st WITH (NOLOCK) ON s.studentid = st.StudentID
        WHERE 
            s.sessdate BETWEEN ? AND ?
            AND s.studentid <> 19790  -- Exclude test student
            AND LOWER(st.FirstName) NOT LIKE '%test%'
            AND LOWER(st.LastName) NOT LIKE '%test%'
            AND LOWER(st.FirstName) NOT LIKE '%sample%'
            AND LOWER(st.LastName) NOT LIKE '%sample%'
        OPTION (MAXDOP 2, OPTIMIZE FOR UNKNOWN)
        """
        
        # Simplified query for better compatibility
        data_query = """
        SELECT
            CONCAT(st.FirstName, ' ', st.LastName) as client_name,
            CONVERT(VARCHAR(10), s.sessdate, 120) as session_date,
            e.Units * 15 as duration,
            0.0 as billable_amount,
            b.Status_name as status,
            'Unknown' as insurance
        FROM 
            sess s WITH (NOLOCK)
        JOIN vwbillingscrubexport_gh e WITH (NOLOCK) ON s.sessid = e.sessionid
        JOIN sessionstatus b WITH (NOLOCK) ON e.BillableStatus = b.status_id
        JOIN Student st WITH (NOLOCK) ON s.studentid = st.StudentID
        WHERE 
            s.sessdate BETWEEN ? AND ?
            AND st.StudentID <> 19790  -- Exclude test student
            AND LOWER(st.FirstName) NOT LIKE '%test%'
            AND LOWER(st.LastName) NOT LIKE '%test%'
            AND LOWER(st.FirstName) NOT LIKE '%sample%'
            AND LOWER(st.LastName) NOT LIKE '%sample%'
        ORDER BY s.sessdate DESC
        OFFSET ? ROWS 
        FETCH NEXT ? ROWS ONLY
        OPTION (MAXDOP 2)
        """

        # Number of retry attempts for database connections
        max_retries = 3
        retry_delay = 1  # seconds
        
        # Variables to store results
        stats_result = None
        data_result = None
        total_records_estimate = 0
        
        # Get summary stats first - this is faster and helps with pagination
        for retry in range(max_retries):
            try:
                with safe_db_connection(timeout=BILLING_QUERY_TIMEOUT, operation_name=f"billing_stats (attempt {retry+1}/{max_retries})") as conn:
                    # Execute stats query with parameters
                    stats_start_time = time.time()
                    stats_result = conn.execute(stats_query, (start_date_str, end_date_str)).fetchone()
                    stats_duration_ms = int((time.time() - stats_start_time) * 1000)
                    logging.info(f"Billing stats query completed in {stats_duration_ms}ms")
                    
                    # Also get total record count for pagination - use fast COUNT query
                    count_query = """
                    SELECT COUNT(*) 
                    FROM sess WITH (NOLOCK)
                    WHERE sessdate BETWEEN ? AND ?
                    """
                    total_records_estimate = conn.execute(count_query, (start_date_str, end_date_str)).fetchval()
                    break  # If successful, break out of retry loop
            except Exception as e:
                logging.error(f"Error on attempt {retry+1}/{max_retries} fetching billing stats: {str(e)}")
                if retry < max_retries - 1:
                    logging.info(f"Retrying in {retry_delay} seconds...")
                    time.sleep(retry_delay)
                    retry_delay *= 2  # Exponential backoff
                else:
                    # If stats query fails, we can still try to get data
                    logging.error(f"Failed to fetch billing stats after {max_retries} attempts")
                    stats_result = None
        
        # Now get the actual data with a separate query - allows for faster paging
        for retry in range(max_retries):
            try:
                with safe_db_connection(timeout=BILLING_QUERY_TIMEOUT, operation_name=f"billing_details (attempt {retry+1}/{max_retries})") as conn:
                    # Execute data query with parameters - simplified parameter list
                    data_start_time = time.time()
                    data_result = conn.execute(data_query, (start_date_str, end_date_str, offset, page_size)).fetchall()
                    data_duration_ms = int((time.time() - data_start_time) * 1000)
                    logging.info(f"Billing data query completed in {data_duration_ms}ms")
                    break  # If successful, break out of retry loop
            except Exception as e:
                logging.error(f"Error on attempt {retry+1}/{max_retries} fetching billing data: {str(e)}")
                if retry < max_retries - 1:
                    logging.info(f"Retrying in {retry_delay} seconds...")
                    time.sleep(retry_delay)
                    retry_delay *= 2  # Exponential backoff
                else:
                    error_msg = f"Failed to fetch billing data after {max_retries} attempts: {str(e)}"
                    logging.error(error_msg)
                    
                    if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                        return jsonify({
                            'error': 'Database query timeout. Please try a smaller date range or try again later.',
                            'details': str(e) if not str(e).startswith('08') else 'Connection timed out'
                        }), 500
                    else:
                        flash('Database query timeout. Please try a smaller date range or try again later.', 'danger')
                        return render_template('billing_details.html',
                                            billing_data=[],
                                            total_sessions=0,
                                            total_billed=0,
                                            unique_clients=0,
                                            avg_session_duration=0,
                                            date_range=date_range)
        
        # Process results and build response
        billing_data = []
        
        if data_result:
            for row in data_result:
                billing_data.append({
                    'client_name': row.client_name,
                    'session_date': row.session_date,
                    'duration': row.duration,
                    'billable_amount': safe_float(row.billable_amount),
                    'status': row.status,
                    'insurance': row.insurance
                })
        
        # Calculate pagination
        total_pages = (total_records_estimate + page_size - 1) // page_size if total_records_estimate else 0
        
        # Prepare response
        if stats_result:
            avg_duration = round(float(stats_result.avg_duration or 0))
            stats = {
                'total_sessions': stats_result.total_sessions or 0,
                'unique_clients': stats_result.unique_clients or 0,
                'total_billed': float(stats_result.total_billed or 0),
                'avg_session_duration': avg_duration,
            }
        else:
            # Fallback if stats query failed
            stats = {
                'total_sessions': len(billing_data),
                'unique_clients': len(set(item['client_name'] for item in billing_data)),
                'total_billed': 0.0,
                'avg_session_duration': 0,
            }
        
        response_data = {
            'billing_data': billing_data,
            'total_sessions': stats['total_sessions'],
            'total_billed': stats['total_billed'],
            'unique_clients': stats['unique_clients'],
            'avg_session_duration': stats['avg_session_duration'],
            'date_range': f"{start_date.strftime('%m/%d/%Y')} - {end_date.strftime('%m/%d/%Y')}",
            'pagination': {
                'current_page': page,
                'page_size': page_size,
                'total_records': total_records_estimate,
                'total_pages': total_pages
            }
        }
        
        # Cache results for future requests - extend cache time to 1 hour
        cache.set(cache_key, response_data, timeout=3600)  # Cache for 1 hour
        
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return jsonify(response_data)
        else:
            # For regular form submissions, render the template with the data
            return render_template('billing_details.html',
                                  billing_data=response_data.get('billing_data', []),
                                  total_sessions=response_data.get('total_sessions', 0),
                                  total_billed=response_data.get('total_billed', 0),
                                  unique_clients=response_data.get('unique_clients', 0),
                                  avg_session_duration=response_data.get('avg_session_duration', 0),
                                  date_range=response_data.get('date_range', date_range))

    # For regular page load without parameters, just render the template with empty data
    return render_template('billing_details.html',
                           billing_data=[],
                           total_sessions=0,
                           total_billed=0,
                           unique_clients=0,
                           avg_session_duration=0,
                           date_range='') 

@billing_bp.route('/reports-dashboard')
@login_required
@cache.cached(timeout=3600, key_prefix=make_cache_key)  # User-specific cache with 1-hour timeout
@check_permission('report.view')
def billing_reports_dashboard():
    """Optimized billing reports dashboard using the most efficient queries."""
    logging.info("Accessed /billing/reports-dashboard route")
    
    # Calculate the first day of the month 4 months ago
    today = datetime.now()
    # Go back 4 months, then set day to 1 to get first day of that month
    # Using simple date calculation that properly handles month boundaries
    four_months_ago = today.replace(day=1)  # First of current month
    for _ in range(4):
        # Go back one month at a time
        if four_months_ago.month == 1:
            four_months_ago = four_months_ago.replace(year=four_months_ago.year-1, month=12)
        else:
            four_months_ago = four_months_ago.replace(month=four_months_ago.month-1)
    
    # Get filter parameters - default to the beginning of month 4 months ago
    start_date = request.args.get('start_date', four_months_ago.strftime('%Y-%m-%d'))
    end_date = request.args.get('end_date', today.strftime('%Y-%m-%d'))
    insurance_id = request.args.get('insurance_id', None, type=int)
    
    logging.info(f"Date range for reports: {start_date} to {end_date}")
    
    # Initialize data containers
    report_data = {
        'insurance_overview': [],
        'authorization_monitoring': [],
        'revenue_by_service': [],
        'monthly_billing_trend': []
    }
    error_message = None
    
    # Set query timeout - using our tested efficient queries which should be fast
    QUERY_TIMEOUT = 30  # seconds
    
    try:
        # 1. Insurance Overview Report - Very Fast (0.2s, 17 records in testing)
        try:
            with safe_db_connection(timeout=QUERY_TIMEOUT, operation_name="insurance_overview_report") as conn:
                query = text("""
                    SELECT 
                        i.Ins_Name AS InsuranceName,
                        COUNT(DISTINCT sess.StudentID) AS PatientCount,
                        COUNT(sess.SessID) AS SessionCount,
                        SUM(DATEDIFF(MINUTE, CAST(sess.starttime AS DATETIME), CAST(sess.endtime AS DATETIME))/15) AS TotalUnits,
                        SUM(DATEDIFF(MINUTE, CAST(sess.starttime AS DATETIME), CAST(sess.endtime AS DATETIME))/60.0) AS TotalHours
                    FROM sess
                    JOIN tblStudentInsuranceLink sil ON sess.StudentID = sil.StudentID 
                        AND sess.sessdate BETWEEN sil.SIL_StartDate AND ISNULL(sil.SIL_EndDate, sess.sessdate)
                    JOIN tblInsurances i ON sil.Ins_ID = i.Ins_ID
                    WHERE sess.Inactive = 0
                        AND sess.sessdate BETWEEN ? AND ?
                        AND (? IS NULL OR sil.Ins_ID = ?)
                    GROUP BY i.Ins_Name
                    ORDER BY PatientCount DESC
                """)
                query_str = str(query)
                cursor = conn.execute(query_str, (start_date, end_date, insurance_id, insurance_id))
                
                # Use SQLAlchemy's built-in row-to-dict functionality
                report_data['insurance_overview'] = [dict(row) for row in cursor.fetchall()]
                logging.info(f"Fetched insurance overview data: {len(report_data['insurance_overview'])} records")
        except Exception as e:
            logging.error(f"Error fetching insurance overview data: {e}")
            error_message = f"Error fetching insurance overview data: {str(e)}"
            
        # 2. Authorization Monitoring - Very Fast (0.3s, 3261 records in testing)
        try:
            with safe_db_connection(timeout=QUERY_TIMEOUT, operation_name="authorization_monitoring") as conn:
                query = text("""
                    SELECT 
                        a.Auth_ID AS Auth_ID,
                        s.StudentID,
                        CONCAT(s.LastName, ', ', s.FirstName) AS PatientName,
                        i.Ins_Name AS InsuranceName,
                        a.Auth_StartDate AS StartDate,
                        a.Auth_EndDate AS EndDate,
                        a.Auth_TotalMonthlyUnitsApproved AS ApprovedUnits,
                        ISNULL(SUM(CASE WHEN sess.sessdate BETWEEN a.Auth_StartDate AND a.Auth_EndDate THEN 
                            DATEDIFF(MINUTE, CAST(sess.starttime AS DATETIME), CAST(sess.endtime AS DATETIME))/15 
                        ELSE 0 END), 0) AS UsedUnits,
                        a.Auth_TotalMonthlyUnitsApproved - ISNULL(SUM(CASE WHEN sess.sessdate BETWEEN a.Auth_StartDate AND a.Auth_EndDate THEN 
                            DATEDIFF(MINUTE, CAST(sess.starttime AS DATETIME), CAST(sess.endtime AS DATETIME))/15 
                        ELSE 0 END), 0) AS RemainingUnits
                    FROM tblAuthorizations a
                    JOIN Student s ON a.StudentID = s.StudentID
                    JOIN tblStudentInsuranceLink sil ON s.StudentID = sil.StudentID 
                        AND a.Auth_StartDate BETWEEN sil.SIL_StartDate AND ISNULL(sil.SIL_EndDate, a.Auth_StartDate)
                    JOIN tblInsurances i ON sil.Ins_ID = i.Ins_ID
                    LEFT JOIN sess ON a.StudentID = sess.StudentID 
                        AND sess.Inactive = 0
                        AND sess.sessdate BETWEEN a.Auth_StartDate AND a.Auth_EndDate
                    WHERE a.Auth_EndDate >= ? 
                        AND a.Auth_StartDate <= ?
                        AND (? IS NULL OR sil.Ins_ID = ?)
                    GROUP BY a.Auth_ID, s.StudentID, s.LastName, s.FirstName, i.Ins_Name, 
                             a.Auth_StartDate, a.Auth_EndDate, a.Auth_TotalMonthlyUnitsApproved
                    HAVING a.Auth_TotalMonthlyUnitsApproved > ISNULL(SUM(CASE WHEN sess.sessdate BETWEEN a.Auth_StartDate AND a.Auth_EndDate THEN 
                        DATEDIFF(MINUTE, CAST(sess.starttime AS DATETIME), CAST(sess.endtime AS DATETIME))/15 
                    ELSE 0 END), 0)
                    ORDER BY a.Auth_EndDate ASC
                """)
                query_str = str(query)
                cursor = conn.execute(query_str, (start_date, end_date, insurance_id, insurance_id))
                
                # Use SQLAlchemy's built-in row-to-dict functionality
                report_data['authorization_monitoring'] = [dict(row) for row in cursor.fetchall()]
                logging.info(f"Fetched authorization monitoring data: {len(report_data['authorization_monitoring'])} records")
        except Exception as e:
            logging.error(f"Error fetching authorization monitoring data: {e}")
            error_message = error_message or f"Error fetching authorization monitoring data: {str(e)}"
            
        # 3. Monthly Billing Trend - Fast (1s, 741 records in testing)
        try:
            with safe_db_connection(timeout=QUERY_TIMEOUT, operation_name="monthly_billing_trend") as conn:
                query = text("""
                    SELECT 
                        FORMAT(sess.sessdate, 'yyyy-MM') AS Month,
                        COUNT(sess.SessID) AS SessionCount,
                        COUNT(DISTINCT sess.StudentID) AS PatientCount,
                        SUM(DATEDIFF(MINUTE, CAST(sess.starttime AS DATETIME), CAST(sess.endtime AS DATETIME))/15) AS TotalUnits
                    FROM sess
                    JOIN tblStudentInsuranceLink sil ON sess.StudentID = sil.StudentID 
                        AND sess.sessdate BETWEEN sil.SIL_StartDate AND ISNULL(sil.SIL_EndDate, sess.sessdate)
                    WHERE sess.Inactive = 0
                        AND sess.sessdate BETWEEN ? AND ?
                        AND (? IS NULL OR sil.Ins_ID = ?)
                    GROUP BY FORMAT(sess.sessdate, 'yyyy-MM')
                    ORDER BY Month
                """)
                query_str = str(query)
                cursor = conn.execute(query_str, (start_date, end_date, insurance_id, insurance_id))
                
                # Use SQLAlchemy's built-in row-to-dict functionality
                report_data['monthly_billing_trend'] = [dict(row) for row in cursor.fetchall()]
                logging.info(f"Fetched monthly billing trend data: {len(report_data['monthly_billing_trend'])} records")
        except Exception as e:
            logging.error(f"Error fetching monthly billing trend data: {e}")
            error_message = error_message or f"Error fetching monthly billing trend data: {str(e)}"
        
        # Fetch insurance providers for filter dropdown (reusing existing code pattern)
        insurance_cache_key = f"insurance_providers_dropdown_data"
        insurance_providers = cache.get(insurance_cache_key)
        if insurance_providers is None:
            try:
                with safe_db_connection(timeout=QUERY_TIMEOUT, operation_name="fetch_insurance_providers") as conn:
                    insurance_query = text("""
                        SELECT Ins_ID, Ins_Name 
                        FROM tblInsurances WITH (NOLOCK)
                        WHERE Ins_Active = 1
                        ORDER BY Ins_Name
                    """)
                    insurance_query_str = str(insurance_query)
                    insurance_result = conn.execute(insurance_query_str).fetchall()
                    insurance_providers = [(row.Ins_ID, row.Ins_Name) for row in insurance_result]
                    # Cache insurance data for 24 hours - they don't change often
                    cache.set(insurance_cache_key, insurance_providers, timeout=86400)
                    logging.info(f"Fetched and cached {len(insurance_providers)} insurance providers for filter")
            except Exception as e:
                logging.error(f"Error fetching insurance providers: {e}")
                error_message = error_message or f"Error fetching insurance providers: {str(e)}"
                insurance_providers = []
        else:
            logging.info("Using cached insurance providers data for dropdown")
            
    except Exception as e:
        logging.error(f"Unexpected error in billing reports dashboard: {e}")
        error_message = f"An unexpected error occurred: {str(e)}"
    
    # Render template with data
    return render_template('billing_reports_dashboard.html',
                          report_data=report_data,
                          insurance_providers=insurance_providers,
                          selected_insurance_id=insurance_id,
                          start_date=start_date,
                          end_date=end_date,
                          error=error_message) 

@billing_bp.route('/provider-utilization')
@login_required
@cache.cached(timeout=1800, key_prefix=make_cache_key)  # User-specific cache with 30-minute timeout
@check_permission('report.view')
def provider_utilization():
    """Provider utilization dashboard showing average weekly hours by role with color coding and per-student breakdown."""
    logging.info("Accessed /billing/provider-utilization route")
    
    # Get filter parameters with dynamic default start date (3 months ago)
    default_start_date = (datetime.now() - timedelta(days=90)).strftime('%Y-%m-%d')
    start_date = request.args.get('start_date', default_start_date)
    end_date = request.args.get('end_date', datetime.now().strftime('%Y-%m-%d'))
    role_filter = request.args.get('role_id', None, type=int)
    location_filter = request.args.get('location_id', None, type=int)
    
    # Validate date range to prevent excessive queries
    try:
        start_dt = datetime.strptime(start_date, '%Y-%m-%d')
        end_dt = datetime.strptime(end_date, '%Y-%m-%d')
        date_diff = (end_dt - start_dt).days
        
        if date_diff > 365:  # Limit to 1 year max
            flash('Date range exceeds 365 days. Please select a shorter period.', 'warning')
            end_date = (start_dt + timedelta(days=365)).strftime('%Y-%m-%d')
    except ValueError:
        flash('Invalid date format. Using default date range.', 'warning')
        start_date = default_start_date
        end_date = datetime.now().strftime('%Y-%m-%d')
    
    # Set query timeout
    QUERY_TIMEOUT = 60  # seconds
    
    # Initialize data containers
    provider_data = []
    locations = []
    roles = [
        {'R_ID': 4, 'R_Name': 'BCBA'},
        {'R_ID': 6, 'R_Name': 'BT'}
    ]
    error_message = None
    
    # Generate cache keys
    provider_cache_key = f"provider_utilization_detailed_{start_date}_{end_date}_{role_filter}_{location_filter}"
    location_cache_key = "office_locations_dropdown"
    
    try:
        # Fetch locations for dropdown (with caching)
        locations = cache.get(location_cache_key)
        if locations is None:
            try:
                with safe_db_connection(timeout=QUERY_TIMEOUT, operation_name="fetch_locations") as conn:
                    location_query = text("""
                        SELECT OfficeLocID, OfficeLocName 
                        FROM tblOfficeLocations WITH (NOLOCK)
                        WHERE OfficeLocActive = 1
                        ORDER BY OfficeLocName
                    """)
                    location_result = conn.execute(str(location_query)).fetchall()
                    locations = [(row.OfficeLocID, row.OfficeLocName) for row in location_result]
                    cache.set(location_cache_key, locations, timeout=86400)  # Cache for 24 hours
                    logging.info(f"Fetched and cached {len(locations)} locations")
            except Exception as e:
                logging.error(f"Error fetching locations: {e}")
                error_message = "Could not fetch locations for filtering."
                locations = []
        
        # Fetch provider utilization data with student breakdown
        cached_data = cache.get(provider_cache_key)
        if cached_data is None:
            try:
                with safe_db_connection(timeout=QUERY_TIMEOUT, operation_name="provider_utilization") as conn:
                    # Modified query to include student details
                    utilization_query = text("""
                        SET DATEFIRST 7;
                        
                        WITH LatestStaffIntake AS (
                            SELECT 
                                SI_LoginID,
                                CorporateAccount,
                                DATETERMINATED,
                                ROW_NUMBER() OVER (PARTITION BY SI_LoginID ORDER BY DateModified DESC) as intake_rn
                            FROM StaffIntake WITH (NOLOCK)
                            WHERE ISNULL(DATETERMINATED,'') = ''
                        ),
                        WeekNumbers AS (
                            -- Generate a row for each week in the date range
                            SELECT 
                                DATEADD(DAY, number * 7, ?) as WeekStart,
                                DATEADD(DAY, (number * 7) + 6, ?) as WeekEnd
                            FROM master.dbo.spt_values
                            WHERE type = 'P' 
                                AND number <= DATEDIFF(WEEK, ?, ?)
                        ),
                        ActualHours AS (
                            SELECT
                                s.TherapistID,
                                s.StudentID,
                                s.CodeID,
                                st.FirstName as StudentFirstName,
                                st.LastName as StudentLastName,
                                SUM(CAST(DATEDIFF(MINUTE, s.starttime, s.endtime) AS FLOAT) / 60.0) AS TotalActualHours,
                                COUNT(DISTINCT CONCAT(DATEPART(YEAR, s.sessdate), '-', FORMAT(DATEPART(ISO_WEEK, s.sessdate), '00'))) AS WeeksWorked
                            FROM sess s WITH (NOLOCK)
                            JOIN Student st WITH (NOLOCK) ON s.StudentID = st.StudentID
                            WHERE s.sessdate BETWEEN ? AND ?
                                AND s.SessionStatus in (4,16)  -- Only count completed/billed sessions
                            GROUP BY s.TherapistID, s.StudentID, s.CodeID, st.FirstName, st.LastName
                        ),
                        PotentialHours AS (
                            SELECT 
                                tsl.TherapistID,
                                tsl.StudentID,
                                tsl.CodeID,
                                st.FirstName as StudentFirstName,
                                st.LastName as StudentLastName,
                                c.Code_Name,
                                -- Calculate total potential hours for the entire period
                                SUM(tsl.Units * c.MinutesPerUnit / 60.0) AS TotalPotentialHours,
                                -- Count number of weeks this assignment spans
                                COUNT(DISTINCT CONCAT(DATEPART(YEAR, w.WeekStart), '-', FORMAT(DATEPART(ISO_WEEK, w.WeekStart), '00'))) AS WeeksAssigned
                            FROM TherapistStudentLink tsl WITH (NOLOCK)
                            JOIN tblCodes c WITH (NOLOCK) ON c.Code_ID = tsl.CodeID
                            JOIN Student st WITH (NOLOCK) ON tsl.StudentID = st.StudentID
                            CROSS JOIN WeekNumbers w
                            WHERE tsl.startDate <= w.WeekEnd 
                                AND tsl.endDate >= w.WeekStart
                            GROUP BY tsl.TherapistID, tsl.StudentID, tsl.CodeID, c.Code_Name, st.FirstName, st.LastName
                        )
                        SELECT DISTINCT 
                            S.LoginID,
                            s.FirstName as ProviderFirstName,
                            s.LastName as ProviderLastName,
                            r.R_Name as Role,
                            r.R_ID as RoleID,
                            ol.OfficeLocName as Location,
                            ol.OfficeLocID as LocationID,
                            COALESCE(ph.StudentFirstName, ah.StudentFirstName) as StudentFirstName,
                            COALESCE(ph.StudentLastName, ah.StudentLastName) as StudentLastName,
                            COALESCE(ph.Code_Name, 'Unknown') as ServiceCode,
                            -- Actual hours worked for this student
                            COALESCE(ah.TotalActualHours, 0) as TotalHours,
                            -- Average weekly hours for this student
                            CASE 
                                WHEN ah.WeeksWorked > 0 
                                THEN COALESCE(ah.TotalActualHours / ah.WeeksWorked, 0)
                                ELSE 0 
                            END AS AverageWeeklyHours,
                            -- Weekly potential hours for this student
                            CASE 
                                WHEN ph.WeeksAssigned > 0 
                                THEN COALESCE(ph.TotalPotentialHours / ph.WeeksAssigned, 0)
                                ELSE 0 
                            END as WeeklyPotentialHours,
                            -- Calculate unused capacity for this student
                            CASE 
                                WHEN ph.WeeksAssigned > 0 
                                THEN COALESCE((ph.TotalPotentialHours / ph.WeeksAssigned) - 
                                    (CASE 
                                        WHEN ah.WeeksWorked > 0 
                                        THEN ah.TotalActualHours / ah.WeeksWorked
                                        ELSE 0 
                                    END), 0)
                                ELSE 0
                            END as AvgWeeklyUnusedHours,
                            COALESCE(ah.WeeksWorked, 0) as WeeksWorked
                        FROM staff s WITH (NOLOCK)
                        INNER JOIN tblPayRate pr WITH (NOLOCK) ON s.LoginID = pr.StaffID
                        INNER JOIN tblroleofficelink rol WITH (NOLOCK) ON s.loginid = rol.loginid 
                        INNER JOIN tblOfficeLocations ol WITH (NOLOCK) ON rol.OfficeLocID = ol.OfficeLocID
                        INNER JOIN Roles r WITH (NOLOCK) ON rol.R_ID = r.R_ID
                        LEFT JOIN LatestStaffIntake si ON s.LoginID = si.SI_LoginID AND si.intake_rn = 1
                        LEFT JOIN PotentialHours ph ON s.LoginID = ph.TherapistID
                        LEFT JOIN ActualHours ah ON s.LoginID = ah.TherapistID 
                            AND ph.StudentID = ah.StudentID 
                            AND ph.CodeID = ah.CodeID
                        WHERE r.R_ID in (4,6)  -- BCBA and BT roles
                            AND (? IS NULL OR r.R_ID = ?)
                            AND (? IS NULL OR ol.OfficeLocID = ?)
                            AND GETDATE() BETWEEN pr.StartDate AND pr.EndDate
                            AND s.firstname NOT LIKE '%Test%' 
                            AND s.lastname NOT LIKE '%Test%'
                            -- Add filter to only include providers with assignments
                            AND EXISTS (
                                SELECT 1 
                                FROM TherapistStudentLink tsl WITH (NOLOCK)
                                WHERE tsl.TherapistID = s.LoginID
                                AND tsl.endDate >= ?  -- Current assignment or ended after start date
                                AND tsl.startDate <= ?  -- Started before or during the period
                                AND tsl.Units > 0  -- Has actual units assigned
                            )
                        ORDER BY ol.OfficeLocName, s.LastName, s.FirstName, r.R_Name,
                                COALESCE(ph.StudentLastName, ah.StudentLastName),
                                COALESCE(ph.StudentFirstName, ah.StudentFirstName)
                        OPTION (MAXDOP 2)
                    """)
                    
                    params = (
                        start_date, start_date,  # WeekNumbers start parameters
                        start_date, end_date,    # WeekNumbers date range for DATEDIFF
                        start_date, end_date,    # For ActualHours date range
                        role_filter, role_filter,  # Role filter
                        location_filter, location_filter,  # Location filter
                        start_date, end_date  # For TherapistStudentLink date range
                    )
                    
                    result = conn.execute(str(utilization_query), params).fetchall()
                    
                    # Convert to structured data with provider and student details
                    provider_data = {}
                    for row in result:
                        provider_id = row.LoginID
                        
                        # Skip if we don't have valid provider data
                        if not provider_id or not row.ProviderFirstName or not row.ProviderLastName:
                            logging.warning(f"Skipping provider with incomplete data: LoginID={provider_id}, FirstName={row.ProviderFirstName}, LastName={row.ProviderLastName}")
                            continue
                        
                        # Initialize provider if not exists
                        if provider_id not in provider_data:
                            provider_data[provider_id] = {
                                'LoginID': provider_id,
                                'FirstName': row.ProviderFirstName,
                                'LastName': row.ProviderLastName,
                                'Role': row.Role,
                                'RoleID': row.RoleID,
                                'Location': row.Location,
                                'LocationID': row.LocationID,
                                'students': [],
                                'TotalHours': 0.0,
                                'AverageWeeklyHours': 0.0,
                                'WeeklyPotentialHours': 0.0,
                                'AvgWeeklyUnusedHours': 0.0,
                                'WeeksWorked': 0
                            }
                        
                        # Add student data if there is a student
                        if row.StudentFirstName and row.StudentLastName:
                            student_data = {
                                'StudentFirstName': row.StudentFirstName,
                                'StudentLastName': row.StudentLastName,
                                'ServiceCode': row.ServiceCode,
                                'TotalHours': safe_float(row.TotalHours),
                                'AverageWeeklyHours': safe_float(row.AverageWeeklyHours),
                                'WeeklyPotentialHours': safe_float(row.WeeklyPotentialHours),
                                'AvgWeeklyUnusedHours': safe_float(row.AvgWeeklyUnusedHours),
                                'WeeksWorked': safe_int(row.WeeksWorked)
                            }
                            provider_data[provider_id]['students'].append(student_data)
                            
                            # Update provider totals
                            provider_data[provider_id]['TotalHours'] += student_data['TotalHours']
                            provider_data[provider_id]['WeeklyPotentialHours'] += student_data['WeeklyPotentialHours']
                            provider_data[provider_id]['AvgWeeklyUnusedHours'] += student_data['AvgWeeklyUnusedHours']
                            provider_data[provider_id]['WeeksWorked'] = max(
                                provider_data[provider_id]['WeeksWorked'],
                                student_data['WeeksWorked']
                            )
                    
                    # Calculate provider averages
                    for provider in provider_data.values():
                        if provider['WeeksWorked'] > 0:
                            provider['AverageWeeklyHours'] = provider['TotalHours'] / provider['WeeksWorked']
                    
                    # Convert to list for template
                    provider_data = list(provider_data.values())
                    
                    # Cache the data for 30 minutes
                    cache.set(provider_cache_key, provider_data, timeout=1800)
                    logging.info(f"Fetched detailed provider utilization data: {len(provider_data)} providers")
                    
            except Exception as e:
                logging.error(f"Error fetching provider utilization data: {e}")
                error_message = f"Could not load provider utilization data: {str(e)}"
                provider_data = []
        else:
            provider_data = cached_data
            logging.info("Using cached provider utilization data")
            
    except Exception as e:
        logging.error(f"Unexpected error in provider utilization route: {e}")
        error_message = f"An unexpected error occurred: {str(e)}"
    
    # Calculate summary statistics with safe division
    bcba_providers = [p for p in provider_data if p['RoleID'] == 4]
    bt_providers = [p for p in provider_data if p['RoleID'] == 6]
    
    summary_stats = {
        'total_providers': len(provider_data),
        'bcba_count': len(bcba_providers),
        'bt_count': len(bt_providers),
        'avg_hours_bcba': round(sum(p['AverageWeeklyHours'] for p in bcba_providers) / len(bcba_providers) if bcba_providers else 0, 1),
        'avg_hours_bt': round(sum(p['AverageWeeklyHours'] for p in bt_providers) / len(bt_providers) if bt_providers else 0, 1),
        'avg_unused_bcba': round(sum(p['AvgWeeklyUnusedHours'] for p in bcba_providers) / len(bcba_providers) if bcba_providers else 0, 1),
        'avg_unused_bt': round(sum(p['AvgWeeklyUnusedHours'] for p in bt_providers) / len(bt_providers) if bt_providers else 0, 1),
        'total_hours': round(sum(p['TotalHours'] for p in provider_data), 1)
    }
    
    return render_template('provider_utilization.html',
                          provider_data=provider_data,
                          locations=locations,
                          roles=roles,
                          selected_role=role_filter,
                          selected_location=location_filter,
                          start_date=start_date,
                          end_date=end_date,
                          summary_stats=summary_stats,
                          error=error_message)

@billing_bp.route('/client-utilization')
@login_required
@cache.cached(timeout=1800, key_prefix=make_cache_key)  # User-specific cache with 30-minute timeout
@check_permission('report.view')
def client_utilization():
    """Client utilization dashboard based on current authorizations."""
    logging.info("Accessed /billing/client-utilization route")
    
    error = None
    client_data = []
    summary_stats = {}
    roles = []
    locations = []

    # Initialize filters from request args
    role_filter = request.args.get('role_id', type=int)
    location_filter = request.args.get('location_id', type=int)
    selected_role = role_filter
    selected_location = location_filter

    # Set query timeout
    QUERY_TIMEOUT = 30  # seconds

    # Generate cache keys
    client_cache_key = f"client_utilization_{role_filter}_{location_filter}"
    location_cache_key = "office_locations_dropdown"
    
    try:
        # Fetch locations for dropdown (reuse existing cache)
        locations = cache.get(location_cache_key)
        if locations is None:
            try:
                with safe_db_connection(timeout=QUERY_TIMEOUT, operation_name="fetch_locations") as conn:
                    location_query = text("""
                        SELECT OfficeLocID, OfficeLocName 
                        FROM tblOfficeLocations WITH (NOLOCK)
                        WHERE OfficeLocActive = 1
                        ORDER BY OfficeLocName
                    """)
                    location_result = conn.execute(str(location_query)).fetchall()
                    locations = [(row.OfficeLocID, row.OfficeLocName) for row in location_result]
                    cache.set(location_cache_key, locations, timeout=86400)  # Cache for 24 hours
                    logging.info(f"Fetched and cached {len(locations)} locations")
            except Exception as e:
                logging.error(f"Error fetching locations: {e}")
                error = "Could not fetch locations for filtering."
                locations = []

        # Get roles (hardcoded for BCBA and BT like provider utilization)
        roles = [
            {'id': 4, 'name': 'BCBA'},
            {'id': 6, 'name': 'BT'}
        ]

        # Fetch client utilization data
        cached_data = cache.get(client_cache_key)
        if cached_data is None:
            try:
                with safe_db_connection(timeout=QUERY_TIMEOUT, operation_name="client_utilization") as conn:
                    # Main query to get client utilization based on current authorizations
                    utilization_query = text("""
                        WITH CurrentAuth AS (
                            -- Get the most recent authorization for each client/code combination
                            SELECT 
                                a.StudentID,
                                acl.CodeID,
                                acl.ACL_WeeklyUnitsApproved,
                                acl.ACL_StartDate,
                                acl.ACL_EndDate,
                                c.Code_Name,
                                c.MinutesPerUnit,
                                ROW_NUMBER() OVER (PARTITION BY a.StudentID, acl.CodeID 
                                                 ORDER BY acl.ACL_EndDate DESC) as rn
                            FROM tblAuthorizations a WITH (NOLOCK)
                            INNER JOIN tblAuthorizeCodeLink acl WITH (NOLOCK) ON a.Auth_ID = acl.Auth_ID
                            INNER JOIN tblCodes c WITH (NOLOCK) ON acl.CodeID = c.Code_ID
                            WHERE (? IS NULL OR c.Code_RoleID = ?)
                            AND GETDATE() BETWEEN a.Auth_StartDate AND a.Auth_EndDate
                        ),
                        WeeklyUsage AS (
                            -- Calculate actual weekly usage from sessions
                            SELECT 
                                s.StudentID,
                                s.CodeID,
                                DATEPART(WEEK, s.SessDate) as WeekNum,
                                DATEPART(YEAR, s.SessDate) as YearNum,
                                SUM(CAST(DATEDIFF(MINUTE, s.starttime, s.endtime) AS FLOAT) / NULLIF(c.MinutesPerUnit, 0)) as WeeklyUnits
                            FROM sess s WITH (NOLOCK)
                            JOIN tblCodes c WITH (NOLOCK) ON s.CodeID = c.Code_ID
                            WHERE s.SessionStatus IN (4, 16) -- Completed sessions only
                            AND s.SessDate >= DATEADD(MONTH, -3, GETDATE()) -- Last 3 months of data
                            AND s.starttime IS NOT NULL 
                            AND s.endtime IS NOT NULL
                            AND s.starttime < s.endtime  -- Ensure valid duration
                            GROUP BY s.StudentID, s.CodeID, DATEPART(WEEK, s.SessDate), DATEPART(YEAR, s.SessDate)
                        ),
                        AggregatedUsage AS (
                            -- Aggregate usage statistics
                            SELECT 
                                wu.StudentID,
                                wu.CodeID,
                                AVG(wu.WeeklyUnits) as AvgWeeklyUnits,
                                MAX(wu.WeeklyUnits) as MaxWeeklyUnits,
                                COUNT(DISTINCT CONCAT(wu.YearNum, '-', wu.WeekNum)) as WeeksWithService
                            FROM WeeklyUsage wu
                            GROUP BY wu.StudentID, wu.CodeID
                        )
                        SELECT 
                            s.StudentID as student_id,
                            s.FirstName as first_name,
                            s.LastName as last_name,
                            loc.OfficeLocName as location,
                            ca.Code_Name as service_code,
                            -- Convert units to hours using MinutesPerUnit
                            ROUND(ca.ACL_WeeklyUnitsApproved * (ca.MinutesPerUnit / 60.0), 1) as authorized_hours_per_week,
                            ROUND(ISNULL(au.AvgWeeklyUnits, 0) * (ca.MinutesPerUnit / 60.0), 1) as average_weekly_hours,
                            ROUND(ISNULL(au.MaxWeeklyUnits, 0) * (ca.MinutesPerUnit / 60.0), 1) as max_weekly_hours,
                            ISNULL(au.WeeksWithService, 0) as weeks_with_service,
                            -- Calculate unused hours
                            ROUND((ca.ACL_WeeklyUnitsApproved - ISNULL(au.AvgWeeklyUnits, 0)) * (ca.MinutesPerUnit / 60.0), 1) as avg_weekly_unused_hours,
                            -- Calculate utilization percentage
                            CASE 
                                WHEN ca.ACL_WeeklyUnitsApproved > 0 
                                THEN ROUND((ISNULL(au.AvgWeeklyUnits, 0) / ca.ACL_WeeklyUnitsApproved) * 100, 1)
                                ELSE 0 
                            END as utilization_percentage,
                            -- Format dates as strings
                            FORMAT(ca.ACL_StartDate, 'MM/dd/yyyy') as auth_start_date,
                            FORMAT(ca.ACL_EndDate, 'MM/dd/yyyy') as auth_end_date,
                            -- Add raw dates for calculations
                            ca.ACL_StartDate as raw_auth_start_date,
                            ca.ACL_EndDate as raw_auth_end_date,
                            -- Calculate days remaining
                            DATEDIFF(day, GETDATE(), ca.ACL_EndDate) as days_remaining
                        FROM CurrentAuth ca
                        INNER JOIN Student s WITH (NOLOCK) ON ca.StudentID = s.StudentID
                        INNER JOIN tblStudentIntake si WITH (NOLOCK) ON s.StudentID = si.SI_StudentID
                        INNER JOIN tblOfficeLocations loc WITH (NOLOCK) ON si.OfficeLocID = loc.OfficeLocID
                        LEFT JOIN AggregatedUsage au ON ca.StudentID = au.StudentID AND ca.CodeID = au.CodeID
                        WHERE ca.rn = 1 -- Only most recent auth
                        AND (? IS NULL OR si.OfficeLocID = ?)
                        ORDER BY s.LastName, s.FirstName, ca.Code_Name
                        OPTION (MAXDOP 2)
                    """)

                    # Execute the query with parameters
                    utilization_params = (
                        role_filter, role_filter,  # For role_id filtering (twice because of IS NULL OR condition)
                        location_filter, location_filter  # For location_id filtering (twice because of IS NULL OR condition)
                    )
                    result = conn.execute(str(utilization_query), utilization_params)

                    # Convert to list of dictionaries using the global helper functions
                    client_data = []
                    for row in result:
                        client_dict = {
                            'student_id': row.student_id,
                            'first_name': row.first_name,
                            'last_name': row.last_name,
                            'location': row.location,
                            'service_code': row.service_code,
                            'authorized_hours_per_week': safe_float(row.authorized_hours_per_week),
                            'average_weekly_hours': safe_float(row.average_weekly_hours),
                            'max_weekly_hours': safe_float(row.max_weekly_hours),
                            'weeks_with_service': safe_int(row.weeks_with_service),
                            'avg_weekly_unused_hours': safe_float(row.avg_weekly_unused_hours),
                            'utilization_percentage': safe_float(row.utilization_percentage),
                            'auth_start_date': row.auth_start_date,
                            'auth_end_date': row.auth_end_date,
                            'raw_auth_start_date': row.raw_auth_start_date,
                            'raw_auth_end_date': row.raw_auth_end_date,
                            'days_remaining': safe_int(row.days_remaining)
                        }
                        client_data.append(client_dict)

                    # Get summary statistics
                    summary_query = text("""
                        WITH CurrentAuth AS (
                            SELECT 
                                a.StudentID,
                                acl.CodeID,
                                acl.ACL_WeeklyUnitsApproved,
                                c.MinutesPerUnit,
                                c.Code_RoleID,
                                ROW_NUMBER() OVER (PARTITION BY a.StudentID, acl.CodeID ORDER BY acl.ACL_EndDate DESC) as rn
                            FROM tblAuthorizations a WITH (NOLOCK)
                            INNER JOIN tblAuthorizeCodeLink acl WITH (NOLOCK) ON a.Auth_ID = acl.Auth_ID
                            INNER JOIN tblCodes c WITH (NOLOCK) ON acl.CodeID = c.Code_ID
                            WHERE GETDATE() BETWEEN a.Auth_StartDate AND a.Auth_EndDate
                            AND (? IS NULL OR c.Code_RoleID = ?)
                        )
                        SELECT
                            COUNT(DISTINCT s.StudentID) as total_clients,
                            SUM(CASE WHEN ca.Code_RoleID = 4 THEN 1 ELSE 0 END) as bcba_count,
                            SUM(CASE WHEN ca.Code_RoleID = 6 THEN 1 ELSE 0 END) as bt_count,
                            ROUND(AVG(ca.ACL_WeeklyUnitsApproved * (ca.MinutesPerUnit / 60.0)), 1) as avg_auth_hours_per_client,
                            ROUND(SUM(ca.ACL_WeeklyUnitsApproved * (ca.MinutesPerUnit / 60.0)), 1) as total_auth_hours
                        FROM CurrentAuth ca
                        INNER JOIN Student s WITH (NOLOCK) ON ca.StudentID = s.StudentID
                        INNER JOIN tblStudentIntake si WITH (NOLOCK) ON s.StudentID = si.SI_StudentID
                        WHERE ca.rn = 1
                        AND (? IS NULL OR si.OfficeLocID = ?)
                        OPTION (MAXDOP 2)
                    """)

                    # Execute summary query with parameters
                    summary_params = (
                        role_filter, role_filter,  # For role_id filtering
                        location_filter, location_filter  # For location_id filtering
                    )
                    summary_result = conn.execute(str(summary_query), summary_params)

                    if summary_result:
                        summary_row = summary_result.fetchone()
                        summary_stats = {
                            'total_clients': safe_int(summary_row.total_clients),
                            'bcba_count': safe_int(summary_row.bcba_count),
                            'bt_count': safe_int(summary_row.bt_count),
                            'avg_auth_hours_per_client': safe_float(summary_row.avg_auth_hours_per_client),
                            'total_auth_hours': safe_float(summary_row.total_auth_hours)
                        }
                    else:
                        summary_stats = {
                            'total_clients': 0,
                            'bcba_count': 0,
                            'bt_count': 0,
                            'avg_auth_hours_per_client': 0.0,
                            'total_auth_hours': 0.0
                        }

                    # Cache the data for 30 minutes
                    cache_data = {
                        'client_data': client_data,
                        'summary_stats': summary_stats
                    }
                    cache.set(client_cache_key, cache_data, timeout=1800)
                    logging.info(f"Fetched client utilization data: {len(client_data)} clients")

            except Exception as e:
                error = f"Error fetching client utilization data: {str(e)}"
                logging.error(f"Client utilization error: {str(e)}", exc_info=True)
                client_data = []
                summary_stats = {
                    'total_clients': 0,
                    'bcba_count': 0,
                    'bt_count': 0,
                    'avg_auth_hours_per_client': 0.0,
                    'total_auth_hours': 0.0
                }
        else:
            # Use cached data
            client_data = cached_data['client_data']
            summary_stats = cached_data['summary_stats']
            logging.info(f"Using cached client utilization data: {len(client_data)} clients")

    except Exception as e:
        error = f"Error loading client utilization dashboard: {str(e)}"
        logging.error(f"Client utilization dashboard error: {str(e)}", exc_info=True)

    return render_template(
        'client_utilization.html',
        client_data=client_data,
        summary_stats=summary_stats,
        roles=roles,
        locations=locations,
        selected_role=selected_role,
        selected_location=selected_location,
        error=error
    )
