"""
Unit tests for API blueprint.

These tests focus on testing the API blueprint routes in isolation,
mocking external dependencies and services.
"""

import pytest
import unittest
from unittest.mock import Mock, patch, MagicMock
import json
import sys
import os

# Add the project root to Python path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../..'))

from app import create_app
from app.blueprints.api import api_bp


class TestAPIBlueprint(unittest.TestCase):
    """Unit tests for API blueprint routes."""
    
    def setUp(self):
        """Set up test client before each test."""
        self.app = create_app('testing')
        self.app.config['TESTING'] = True
        self.app.config['SECRET_KEY'] = 'testing-key'
        self.app.config['WTF_CSRF_ENABLED'] = False
        self.client = self.app.test_client()
        self.app_context = self.app.app_context()
        self.app_context.push()
        
    def tearDown(self):
        """Clean up after each test."""
        self.app_context.pop()
    
    def test_api_blueprint_registration(self):
        """Test that API blueprint is properly registered."""
        # Check that the blueprint is registered
        blueprint_names = [bp.name for bp in self.app.blueprints.values()]
        self.assertIn('api', blueprint_names)
    
    def test_health_check_endpoint(self):
        """Test the health check endpoint returns expected response."""
        response = self.client.get('/api/health')
        
        # Should return 200 OK
        self.assertEqual(response.status_code, 200)
        
        # Should return JSON
        self.assertEqual(response.content_type, 'application/json')
        
        # Should contain expected fields
        data = json.loads(response.data)
        self.assertIn('status', data)
        self.assertIn('timestamp', data)
        self.assertEqual(data['status'], 'healthy')
    
    @patch('app.blueprints.api.PhoneLogService')
    def test_phone_log_metadata_endpoint(self, mock_service_class):
        """Test phone log metadata endpoint."""
        # Mock the service
        mock_service = MagicMock()
        mock_service_class.return_value = mock_service
        mock_service.get_call_types.return_value = [
            {'id': 1, 'name': 'Initial Contact', 'description': 'First call to parent'}
        ]
        
        # Mock authentication
        with self.client.session_transaction() as sess:
            sess['user_id'] = 1
            sess['username'] = 'testuser'
            sess['permissions'] = ['phone_log.view']
        
        response = self.client.get('/api/phone-log/metadata')
        
        # Should return 200 OK
        self.assertEqual(response.status_code, 200)
        
        # Should return JSON with call types
        data = json.loads(response.data)
        self.assertIn('call_types', data)
        self.assertEqual(len(data['call_types']), 1)
        self.assertEqual(data['call_types'][0]['name'], 'Initial Contact')
    
    def test_phone_log_metadata_unauthenticated(self):
        """Test phone log metadata endpoint without authentication."""
        response = self.client.get('/api/phone-log/metadata')
        
        # Should redirect to login (302) or return 401
        self.assertIn(response.status_code, [302, 401])
    
    @patch('app.blueprints.api.PhoneLogService')
    def test_pending_calls_endpoint(self, mock_service_class):
        """Test pending calls endpoint."""
        # Mock the service response
        mock_service = MagicMock()
        mock_service_class.return_value = mock_service
        
        mock_response = MagicMock()
        mock_response.calls = [
            {
                'client_id': 123,
                'client_name': 'John Doe',
                'guardian_name': 'Jane Doe',
                'guardian_phone': '555-0123',
                'priority': 'overdue'
            }
        ]
        mock_response.metadata = {'total_count': 1}
        mock_service.get_pending_calls.return_value = mock_response
        
        # Mock authentication
        with self.client.session_transaction() as sess:
            sess['user_id'] = 1
            sess['username'] = 'testuser'
            sess['permissions'] = ['phone_log.view']
        
        response = self.client.get('/api/phone-log/pending-calls')
        
        # Should return 200 OK
        self.assertEqual(response.status_code, 200)
        
        # Should return JSON with calls
        data = json.loads(response.data)
        self.assertIn('calls', data)
        self.assertIn('metadata', data)
        self.assertEqual(len(data['calls']), 1)
        self.assertEqual(data['calls'][0]['client_name'], 'John Doe')
    
    @patch('app.blueprints.api.PhoneLogService')
    def test_create_phone_log_endpoint(self, mock_service_class):
        """Test create phone log endpoint."""
        # Mock the service
        mock_service = MagicMock()
        mock_service_class.return_value = mock_service
        mock_service.create_phone_log_entry.return_value = True
        
        # Mock authentication
        with self.client.session_transaction() as sess:
            sess['user_id'] = 1
            sess['username'] = 'testuser'
            sess['permissions'] = ['phone_log.create']
        
        # Test data
        log_data = {
            'client_id': 123,
            'call_type_id': 1,
            'notes': 'Test call notes',
            'duration_minutes': 15
        }
        
        response = self.client.post('/api/phone-log/create',
                                  data=json.dumps(log_data),
                                  content_type='application/json')
        
        # Should return 201 Created
        self.assertEqual(response.status_code, 201)
        
        # Should return success response
        data = json.loads(response.data)
        self.assertIn('success', data)
        self.assertTrue(data['success'])
    
    def test_create_phone_log_invalid_data(self):
        """Test create phone log endpoint with invalid data."""
        # Mock authentication
        with self.client.session_transaction() as sess:
            sess['user_id'] = 1
            sess['username'] = 'testuser'
            sess['permissions'] = ['phone_log.create']
        
        # Invalid data (missing required fields)
        invalid_data = {
            'notes': 'Test notes'
            # Missing client_id, call_type_id, etc.
        }
        
        response = self.client.post('/api/phone-log/create',
                                  data=json.dumps(invalid_data),
                                  content_type='application/json')
        
        # Should return 400 Bad Request
        self.assertEqual(response.status_code, 400)
        
        # Should return error response
        data = json.loads(response.data)
        self.assertIn('error', data)
    
    def test_create_phone_log_no_permission(self):
        """Test create phone log endpoint without proper permissions."""
        # Mock authentication without phone_log.create permission
        with self.client.session_transaction() as sess:
            sess['user_id'] = 1
            sess['username'] = 'testuser'
            sess['permissions'] = ['phone_log.view']  # No create permission
        
        log_data = {
            'client_id': 123,
            'call_type_id': 1,
            'notes': 'Test call notes',
            'duration_minutes': 15
        }
        
        response = self.client.post('/api/phone-log/create',
                                  data=json.dumps(log_data),
                                  content_type='application/json')
        
        # Should return 403 Forbidden
        self.assertEqual(response.status_code, 403)
    
    def test_api_error_handling(self):
        """Test that API endpoints handle errors gracefully."""
        # Test with malformed JSON
        response = self.client.post('/api/phone-log/create',
                                  data='invalid json',
                                  content_type='application/json')
        
        # Should return 400 Bad Request for malformed JSON
        self.assertEqual(response.status_code, 400)
    
    def test_api_content_type_validation(self):
        """Test that API endpoints validate content type."""
        # Mock authentication
        with self.client.session_transaction() as sess:
            sess['user_id'] = 1
            sess['username'] = 'testuser'
            sess['permissions'] = ['phone_log.create']
        
        # Send data with wrong content type
        response = self.client.post('/api/phone-log/create',
                                  data='{"client_id": 123}',
                                  content_type='text/plain')
        
        # Should return 400 Bad Request for wrong content type
        self.assertEqual(response.status_code, 400)


if __name__ == '__main__':
    unittest.main()
