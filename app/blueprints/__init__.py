from .clients import clients_bp
from .billing import billing_bp
from .providers import providers_bp
from .payroll import payroll_bp
from .api import api_bp
from .sessions import sessions_bp
from .authorizations import authorizations_bp

def register_blueprints(app):
    """Register all blueprints with the Flask app."""
    app.register_blueprint(clients_bp)
    app.register_blueprint(billing_bp)
    app.register_blueprint(providers_bp)
    app.register_blueprint(payroll_bp)
    app.register_blueprint(api_bp)
    app.register_blueprint(sessions_bp)
    app.register_blueprint(authorizations_bp)
    # Add other blueprints here as they're created 