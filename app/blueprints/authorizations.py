from flask import Blueprint, render_template, jsonify, request, session, redirect, url_for, flash
from app.auth import login_required
from app.middleware.permission_check import check_permission
from app.database import get_read_only_connection, get_write_connection
from app import cache
from app import make_cache_key
from contextlib import contextmanager
from app.utils.encryption import decrypt_data
import base64
from datetime import datetime, timedelta
from functools import wraps
import pyodbc
from app.models import User
import logging

# Create the blueprint with a url_prefix
authorizations_bp = Blueprint('authorizations', __name__, url_prefix='/authorizations')

# Set up logging
logger = logging.getLogger(__name__)

@contextmanager
def get_db_connection(write_access=False):
    """
    Get appropriate database connection based on access type.
    Uses get_write_connection for write operations and get_read_only_connection for read operations.
    """
    if write_access:
        # Use get_write_connection for write operations
        with get_write_connection() as conn:
            yield conn
    else:
        # Use read-only connection for read operations
        conn = get_read_only_connection()
        try:
            yield conn
        finally:
            if conn:
                conn.close()

@authorizations_bp.route('/add/<int:student_id>', methods=['POST'])
@login_required
@check_permission('authorizations.create')
def add_authorization(student_id):
    """Add a new authorization for a client."""
    try:
        form_data = request.form
        student_ins_link_id = form_data.get('student_ins_link_id')
        auth_number = form_data.get('auth_number')
        auth_date_requested = form_data.get('auth_date_requested') or None
        auth_notes = form_data.get('auth_notes')
        auth_start_date = form_data.get('auth_start_date') or None
        auth_end_date = form_data.get('auth_end_date') or None
        auth_total_monthly_units = form_data.get('auth_total_monthly_units') or None
        might_appeal = form_data.get('might_appeal') == 'on'
        last_modified_by = session.get('user_id')

        with get_db_connection(write_access=True) as conn:
            cursor = conn.cursor()
            cursor.execute("""
                EXEC sp_Authorizations_AddNew 
                    @StudentID = %s, 
                    @StudentInsLinkID = %s, 
                    @Auth_Number = %s, 
                    @Auth_DateRequested = %s, 
                    @Auth_Notes = %s, 
                    @Auth_StartDate = %s, 
                    @Auth_EndDate = %s, 
                    @Auth_LastModifiedBy = %s, 
                    @Auth_TotalMonthlyUnitsApproved = %s, 
                    @MightAppeal = %s
            """, (student_id, student_ins_link_id, auth_number, auth_date_requested, auth_notes, 
                  auth_start_date, auth_end_date, last_modified_by, auth_total_monthly_units, might_appeal))
            conn.commit()
            
            flash("Authorization added successfully!", "success")
            return redirect(url_for('authorizations.client_authorizations_detail', student_id=student_id))
            
    except Exception as e:
        flash(f"Error adding authorization: {str(e)}", "danger")
        return redirect(url_for('authorizations.client_authorizations_detail', student_id=student_id))

@authorizations_bp.route('/update/<int:auth_id>', methods=['POST'])
@login_required
@check_permission('authorizations.edit')
def update_authorization(auth_id):
    """Update an existing authorization."""
    try:
        form_data = request.form
        student_id = form_data.get('student_id')
        student_ins_link_id = form_data.get('student_ins_link_id')
        auth_number = form_data.get('auth_number')
        auth_notes = form_data.get('auth_notes')
        auth_start_date = form_data.get('auth_start_date') or None
        auth_end_date = form_data.get('auth_end_date') or None
        auth_total_monthly_units = form_data.get('auth_total_monthly_units') or None
        might_appeal = form_data.get('might_appeal') == 'on'
        last_modified_by = session.get('user_id')

        with get_db_connection(write_access=True) as conn:
            cursor = conn.cursor()
            cursor.execute("""
                EXEC sp_Authorizations_Update 
                    @StudentID = %s, 
                    @Auth_ID = %s, 
                    @StudentInsLinkID = %s, 
                    @Auth_Number = %s, 
                    @Auth_Notes = %s, 
                    @Auth_StartDate = %s, 
                    @Auth_EndDate = %s, 
                    @Auth_LastModifiedBy = %s, 
                    @Auth_TotalMonthlyUnitsApproved = %s, 
                    @MightAppeal = %s
            """, (student_id, auth_id, student_ins_link_id, auth_number, auth_notes, 
                  auth_start_date, auth_end_date, last_modified_by, auth_total_monthly_units, might_appeal))
            conn.commit()
            
            flash("Authorization updated successfully!", "success")
            return redirect(url_for('authorizations.client_authorizations_detail', student_id=student_id))
            
    except Exception as e:
        flash(f"Error updating authorization: {str(e)}", "danger")
        return redirect(url_for('authorizations.client_authorizations_detail', student_id=student_id))

@authorizations_bp.route('/delete/<int:auth_id>/<int:student_id>', methods=['POST'])
@login_required
@check_permission('authorizations.delete')
def delete_authorization(auth_id, student_id):
    """Delete an authorization."""
    try:
        with get_db_connection(write_access=True) as conn:
            cursor = conn.cursor()
            cursor.execute("EXEC sp_Authorizations_DeleteByAuthID @Auth_ID = %s", (auth_id,))
            conn.commit()
            
            flash("Authorization deleted successfully!", "success")
            return redirect(url_for('authorizations.client_authorizations_detail', student_id=student_id))
            
    except Exception as e:
        flash(f"Error deleting authorization: {str(e)}", "danger")
        return redirect(url_for('authorizations.client_authorizations_detail', student_id=student_id))

@authorizations_bp.route('/clone/<int:auth_id>/<int:student_id>', methods=['POST'])
@login_required
@check_permission('authorizations.create')
def clone_authorization(auth_id, student_id):
    """Clone an existing authorization."""
    try:
        with get_db_connection(write_access=True) as conn:
            cursor = conn.cursor()
            cursor.execute("EXEC sp_CloneAuthorization @AuthID = %s, @LastModifiedBy = %s", 
                          (auth_id, session.get('user_id')))
            conn.commit()
            
            flash("Authorization cloned successfully!", "success")
            return redirect(url_for('authorizations.client_authorizations_detail', student_id=student_id))
            
    except Exception as e:
        flash(f"Error cloning authorization: {str(e)}", "danger")
        return redirect(url_for('authorizations.client_authorizations_detail', student_id=student_id))

# NEW: Route to select a client for viewing authorizations
@authorizations_bp.route('/authorizations/clients', methods=['GET'])
@login_required
@cache.cached(timeout=1800, key_prefix=make_cache_key)  # User-specific cache with 30-minute timeout
@check_permission('authorizations.view')
def client_authorizations_list():
    """Display a page to select a client to view their authorizations."""
    clients = []
    try:
        with get_db_connection() as conn:
            cursor = conn.cursor()
            # Ensure you have a stored procedure like sp_Students_GetBasicInfo
            # that returns studentId and StudentName
            cursor.execute("EXEC sp_Students_GetBasicInfo") # UPDATED SPROC NAME HERE
            clients_data = cursor.fetchall()
            # Construct a list of dicts for easier template rendering
            # Make sure these keys match the column names from your SPROC
            clients = [{'StudentID': row.studentId, 'FullName': row.StudentName} for row in clients_data]
    except Exception as e:
        flash("Error loading client list. Please try again later.", "danger")
        # Redirect to a general page if clients can't be loaded
        return redirect(url_for('routes.dashboard')) # Adjust if your dashboard route is different

    return render_template('client_authorizations.html', clients=clients, student_id=None)

@authorizations_bp.route('/')
def index():
    try:
        # Get database connection
        conn = get_read_only_connection()
        cursor = conn.cursor()
        
        # Query for authorizations with insurance and auth type info
        auth_query = """
        SELECT 
            a.Auth_ID,
            a.StudentID,
            a.StudentInsLinkID,
            a.Auth_StartDate,
            a.Auth_EndDate,
            a.Auth_DateRequested,
            a.RequestedBy,
            a.Auth_DateApproved,
            a.Auth_Notes,
            a.Auth_DateCreated,
            a.Entity,
            a.Auth_TotalMonthlyUnitsApproved,
            a.Auth_DateModified,
            a.Auth_LastModifiedBy,
            i.InsuranceName,
            at.AuthTypeReadOnly,
            a.isCurrent,
            a.isAssigned
        FROM Authorizations a
        LEFT JOIN StudentInsuranceLink sil ON a.StudentInsLinkID = sil.StudentInsLinkID
        LEFT JOIN Insurance i ON sil.InsuranceID = i.InsuranceID
        LEFT JOIN AuthTypes at ON a.AuthTypeID = at.AuthTypeID
        ORDER BY a.Auth_ID DESC
        """
        
        cursor.execute(auth_query)
        auth_rows = cursor.fetchall()
        
        # Process authorization data using column names instead of indices
        authorizations = []
        for row in auth_rows:
            auth_obj = type('Authorization', (), {
                'Auth_ID': getattr(row, 'Auth_ID', None),
                'StudentID': getattr(row, 'StudentID', None),
                'StudentInsLinkID': getattr(row, 'StudentInsLinkID', None),
                'Auth_StartDate': getattr(row, 'Auth_StartDate', None),
                'Auth_EndDate': getattr(row, 'Auth_EndDate', None),
                'Auth_DateRequested': getattr(row, 'Auth_DateRequested', None),
                'RequestedBy': getattr(row, 'RequestedBy', None),
                'Auth_DateApproved': getattr(row, 'Auth_DateApproved', None),
                'Auth_Notes': getattr(row, 'Auth_Notes', '') or '',
                'Auth_DateCreated': getattr(row, 'Auth_DateCreated', None),
                'Entity': getattr(row, 'Entity', None),
                'Auth_TotalMonthlyUnitsApproved': getattr(row, 'Auth_TotalMonthlyUnitsApproved', None),
                'Auth_DateModified': getattr(row, 'Auth_DateModified', None),
                'Auth_LastModifiedBy': getattr(row, 'Auth_LastModifiedBy', None),
                'InsuranceName': getattr(row, 'InsuranceName', 'Unknown') or 'Unknown',
                'AuthTypeReadOnly': getattr(row, 'AuthTypeReadOnly', None),
                'isCurrent': getattr(row, 'isCurrent', False),
                'isAssigned': getattr(row, 'isAssigned', False),
                'Status': 'Active' if getattr(row, 'isCurrent', False) else 'Inactive',
                'Auth_Number': f"AUTH-{getattr(row, 'Auth_ID', '')}" if getattr(row, 'Auth_ID', None) else '',
            })()
            authorizations.append(auth_obj)
        
        return render_template('authorizations/index.html', authorizations=authorizations)
        
    except Exception as e:
        logger.error(f"Error loading authorizations index: {e}", exc_info=True)
        return f"Error loading authorizations: {str(e)}", 500
    finally:
        if 'conn' in locals() and conn:
            conn.close()

def get_client_authorizations_data(student_id):
    """Get authorization data for a client - shared method for both cached and refresh routes"""
    
    client_name = "Unknown Client"
    processed_auth_rows = [] 
    auth_types = []
    
    insurances_for_dropdown = [] 
    insurance_display_name_map = {} 
    
    insurance_dates = {}
    insurance_details = {}

    conn = None
    try:
        # Use single connection for all operations
        conn = get_read_only_connection()
        cursor = conn.cursor()
        
        # Block 1: Get Client Name
        try:
            cursor.execute("EXEC sp_Students_GetByID ?", student_id)
            student = cursor.fetchone()
            if student:
                # Use column names instead of indices
                first_name = getattr(student, 'firstname', '') or getattr(student, 'FirstName', '')
                last_name = getattr(student, 'lastname', '') or getattr(student, 'LastName', '')
                client_name = f"{first_name} {last_name}".strip()
        except Exception as e:
            pass  # Silent error handling

        # Block 2: Get Authorizations
        raw_auth_rows = [] 
        try:
            cursor.execute("EXEC sp_Authorizations_GetByStudentID ?, ?", student_id, 0)

            fetched_successfully = False
            try:
                raw_auth_rows = cursor.fetchall() 
                fetched_successfully = True
            except pyodbc.ProgrammingError as e:
                pass  # Silent error handling

            if not fetched_successfully:
                try:
                    while True:
                        has_next_data_set = cursor.nextset()
                        if has_next_data_set:
                            try:
                                raw_auth_rows = cursor.fetchall() 
                                fetched_successfully = True
                                break
                            except pyodbc.ProgrammingError as e_next_fetch:
                                pass  # Silent error handling
                        else:
                            break
                except pyodbc.ProgrammingError as e_nextset_itself:
                    pass  # Silent error handling

            if not fetched_successfully:
                raw_auth_rows = []

            try:
                while cursor.nextset():
                    pass
            except pyodbc.ProgrammingError as e_final_clear:
                pass  # Silent error handling

        except Exception as e:
            raw_auth_rows = []

        # Block 3: Get Authorization Types
        try:
            cursor.execute("EXEC sp_EnmAuthorizationType_GetActive")
            auth_types = cursor.fetchall()
        except Exception as e:
            auth_types = []

        # Block 4: Get Insurances
        try:
            cursor.execute("EXEC sp_StudentInsuranceLink_GetByStudentID ?", student_id)
            insurance_rows_from_sp = cursor.fetchall()
            
            for i, row_ins_sp in enumerate(insurance_rows_from_sp):
                sil_id = None
                base_insurance_name = 'Unknown Insurance Name'

                try:
                    # Get StudentInsLinkID
                    if hasattr(row_ins_sp, 'StudentInsLinkID'):
                        sil_id = row_ins_sp.StudentInsLinkID
                    elif len(row_ins_sp) > 0 and row_ins_sp[0] is not None:
                        sil_id = row_ins_sp[0]
                    else:
                        continue

                    # Get insurance name
                    possible_name_attributes = ['Ins_Name', 'ins_name', 'InsuranceName', 'insurance_name', 'InsName', 'CompanyName', 'InsuranceCompanyName']
                    found_textual_name = False
                    for attr_name in possible_name_attributes:
                        if hasattr(row_ins_sp, attr_name):
                            potential_name = getattr(row_ins_sp, attr_name)
                            if potential_name and isinstance(potential_name, str) and not potential_name.isdigit():
                                base_insurance_name = potential_name
                                found_textual_name = True
                                break

                    # Get additional fields
                    group_id = None
                    if hasattr(row_ins_sp, 'GroupID'): 
                        group_id = str(row_ins_sp.GroupID) if row_ins_sp.GroupID else None
                    elif len(row_ins_sp) > 2: 
                        group_id = str(row_ins_sp[2]) if row_ins_sp[2] else None
                    
                    member_id_enc = None
                    if hasattr(row_ins_sp, 'MemberID'): 
                        member_id_enc = row_ins_sp.MemberID
                    elif len(row_ins_sp) > 3: 
                        member_id_enc = row_ins_sp[3]
                    member_id = decrypt_data(member_id_enc) if member_id_enc else None

                    # Process dates...
                    raw_start_date_obj = getattr(row_ins_sp, 'EffectiveDate', row_ins_sp[6] if len(row_ins_sp) > 6 else None)
                    raw_end_date_obj = getattr(row_ins_sp, 'TermDate', row_ins_sp[7] if len(row_ins_sp) > 7 else None)

                    # Enhanced date formatting to handle string dates from database
                    formatted_start_date_str = ""
                    start_date_val = None  # For validation purposes
                    if raw_start_date_obj:
                        if hasattr(raw_start_date_obj, 'strftime'):
                            # It's a datetime/date object
                            formatted_start_date_str = raw_start_date_obj.strftime('%m/%d/%Y')
                            start_date_val = raw_start_date_obj.date() if hasattr(raw_start_date_obj, 'date') and not isinstance(raw_start_date_obj, datetime.date) else raw_start_date_obj
                        elif isinstance(raw_start_date_obj, str) and raw_start_date_obj.strip():
                            # It's a string - database is returning MM/DD/YYYY format strings
                            raw_start_date_obj = raw_start_date_obj.strip()
                            try:
                                # Try parsing MM/DD/YYYY format (what we're seeing from DB)
                                parsed_date = datetime.strptime(raw_start_date_obj, '%m/%d/%Y')
                                formatted_start_date_str = parsed_date.strftime('%m/%d/%Y')
                                start_date_val = parsed_date.date()
                            except ValueError:
                                try:
                                    # Try YYYY-MM-DD format as fallback
                                    parsed_date = datetime.strptime(raw_start_date_obj, '%Y-%m-%d')
                                    formatted_start_date_str = parsed_date.strftime('%m/%d/%Y')
                                    start_date_val = parsed_date.date()
                                except ValueError:
                                    # If all parsing fails, just use the original string if it looks like a date
                                    if '/' in raw_start_date_obj or '-' in raw_start_date_obj:
                                        formatted_start_date_str = raw_start_date_obj

                    formatted_end_date_str = ""
                    end_date_val = None  # For validation purposes
                    if raw_end_date_obj:
                        if hasattr(raw_end_date_obj, 'strftime'):
                            # It's a datetime/date object
                            formatted_end_date_str = raw_end_date_obj.strftime('%m/%d/%Y')
                            end_date_val = raw_end_date_obj.date() if hasattr(raw_end_date_obj, 'date') and not isinstance(raw_end_date_obj, datetime.date) else raw_end_date_obj
                        elif isinstance(raw_end_date_obj, str) and raw_end_date_obj.strip():
                            # It's a string - database is returning MM/DD/YYYY format strings
                            raw_end_date_obj = raw_end_date_obj.strip()
                            try:
                                # Try parsing MM/DD/YYYY format (what we're seeing from DB)
                                parsed_date = datetime.strptime(raw_end_date_obj, '%m/%d/%Y')
                                formatted_end_date_str = parsed_date.strftime('%m/%d/%Y')
                                end_date_val = parsed_date.date()
                            except ValueError:
                                try:
                                    # Try YYYY-MM-DD format as fallback
                                    parsed_date = datetime.strptime(raw_end_date_obj, '%Y-%m-%d')
                                    formatted_end_date_str = parsed_date.strftime('%m/%d/%Y')
                                    end_date_val = parsed_date.date()
                                except ValueError:
                                    # If all parsing fails, just use the original string if it looks like a date
                                    if '/' in raw_end_date_obj or '-' in raw_end_date_obj:
                                        formatted_end_date_str = raw_end_date_obj

                    member_id_decrypted_str = decrypt_data(member_id_enc) if member_id_enc else ""

                    # Create comprehensive display information for dropdown
                    group_id_str = str(group_id) if group_id else "N/A"

                    if sil_id is not None: 
                        ins_dict_for_dropdown = {
                            'StudentInsLinkID': sil_id,
                            'InsuranceName': str(base_insurance_name),
                            'StartDate': formatted_start_date_str if formatted_start_date_str else "N/A",
                            'EndDate': formatted_end_date_str if formatted_end_date_str else "Present", 
                            'MemberID': member_id_decrypted_str if member_id_decrypted_str else "N/A",
                            'GroupID': group_id_str
                        }
                        
                        insurances_for_dropdown.append(ins_dict_for_dropdown)
                        insurance_display_name_map[sil_id] = str(base_insurance_name) # Keep simple name for main table
                        
                        # Populate validation data (previously done in Block 5)
                        insurance_dates[sil_id] = {'start': start_date_val, 'end': end_date_val}
                        insurance_details[sil_id] = {'member_id': member_id, 'group_id': group_id}
                        
                except Exception as e_ins_proc:
                    pass  # Silent error handling
                    
        except Exception as e:
            pass  # Silent error handling

        # Block 6: Convert raw_auth_rows to list of dicts and add insurance data
        for raw_auth_row in raw_auth_rows:
            auth_id_value = getattr(raw_auth_row, 'Auth_ID', None)
            
            # Skip authorizations with missing Auth_ID
            if not auth_id_value:
                continue

            auth_dict = {
                'Auth_ID': auth_id_value,
                'StudentID': getattr(raw_auth_row, 'StudentID', None),
                'StudentInsLinkID': getattr(raw_auth_row, 'StudentInsLinkID', None),
                'Auth_StartDate': getattr(raw_auth_row, 'Auth_StartDate', None), 
                'Auth_EndDate': getattr(raw_auth_row, 'Auth_EndDate', None),     
                'Auth_DateRequested': getattr(raw_auth_row, 'Auth_DateRequested', None),
                'RequestedBy': getattr(raw_auth_row, 'RequestedBy', None),
                'Auth_DateApproved': getattr(raw_auth_row, 'Auth_DateApproved', None),
                'Auth_Notes': getattr(raw_auth_row, 'Auth_Notes', None),
                'Auth_DateCreated': getattr(raw_auth_row, 'Auth_DateCreated', None),
                'Entity': getattr(raw_auth_row, 'Entity', None),
                'Auth_TotalMonthlyUnitsApproved': getattr(raw_auth_row, 'Auth_TotalMonthlyUnitsApproved', None),
                'Auth_DateModified': getattr(raw_auth_row, 'Auth_DateModified', None),
                'Auth_LastModifiedBy': getattr(raw_auth_row, 'Auth_LastModifiedBy', None),
                'Auth_Number': getattr(raw_auth_row, 'Auth_Number', None), 
                'isCurrent': getattr(raw_auth_row, 'isCurrent', False), 
                'MightAppeal': getattr(raw_auth_row, 'MightAppeal', None)
            }

            current_sil_id_for_auth = auth_dict.get('StudentInsLinkID')
            auth_dict['InsuranceName'] = insurance_display_name_map.get(current_sil_id_for_auth, 'N/A (Link ID not in map)') 

            ins_dates_for_auth = insurance_dates.get(current_sil_id_for_auth, {})
            ins_details_for_auth = insurance_details.get(current_sil_id_for_auth, {})
            
            auth_dict['insurance_start_date'] = ins_dates_for_auth.get('start') 
            auth_dict['insurance_end_date'] = ins_dates_for_auth.get('end')     
            auth_dict['member_id'] = ins_details_for_auth.get('member_id')
            auth_dict['group_id'] = ins_details_for_auth.get('group_id')

            auth_start_date_obj = auth_dict.get('Auth_StartDate')
            auth_end_date_obj = auth_dict.get('Auth_EndDate')
            ins_start_date_obj = auth_dict.get('insurance_start_date')
            ins_end_date_obj = auth_dict.get('insurance_end_date')

            has_date_issue = False
            date_issue_reason = ""

            # Convert datetime objects to date objects if needed for comparison
            if auth_start_date_obj and hasattr(auth_start_date_obj, 'date'):
                auth_start_date_obj = auth_start_date_obj.date()
            if auth_end_date_obj and hasattr(auth_end_date_obj, 'date'):
                auth_end_date_obj = auth_end_date_obj.date()
            if ins_start_date_obj and hasattr(ins_start_date_obj, 'date'):
                ins_start_date_obj = ins_start_date_obj.date()
            if ins_end_date_obj and hasattr(ins_end_date_obj, 'date'):
                ins_end_date_obj = ins_end_date_obj.date()

            # Check authorization start date issues (only needs insurance start date)
            if auth_start_date_obj and ins_start_date_obj:
                # Case 1: Authorization starts before insurance coverage begins
                if auth_start_date_obj < ins_start_date_obj:
                    has_date_issue = True
                    date_issue_reason = "Authorization starts before insurance coverage begins"

            # Check authorization start date against insurance end date (only if insurance has an end date)
            if auth_start_date_obj and ins_end_date_obj and not has_date_issue:
                # Case 2: Authorization starts after insurance coverage ends
                if auth_start_date_obj > ins_end_date_obj:
                    has_date_issue = True
                    date_issue_reason = "Authorization starts after insurance coverage ends"

            # Check authorization end date issues
            if auth_end_date_obj and ins_start_date_obj and not has_date_issue:
                # Case 3: Authorization ends before insurance coverage begins
                if auth_end_date_obj < ins_start_date_obj:
                    has_date_issue = True
                    date_issue_reason = "Authorization ends before insurance coverage begins"

            # Check authorization end date against insurance end date (only if insurance has an end date)
            if auth_end_date_obj and ins_end_date_obj and not has_date_issue:
                # Case 4: Authorization ends after insurance coverage ends
                if auth_end_date_obj > ins_end_date_obj:
                    has_date_issue = True
                    date_issue_reason = "Authorization ends after insurance coverage ends"

            # Additional comprehensive check: Authorization period vs insurance period
            if (auth_start_date_obj and auth_end_date_obj and ins_start_date_obj and not has_date_issue):
                # Check if entire authorization period is before insurance starts
                if auth_end_date_obj < ins_start_date_obj:
                    has_date_issue = True
                    date_issue_reason = "Authorization period is entirely before insurance coverage"
                # Check if entire authorization period is after insurance ends (only if insurance has end date)
                elif ins_end_date_obj and auth_start_date_obj > ins_end_date_obj:
                    has_date_issue = True
                    date_issue_reason = "Authorization period is entirely after insurance coverage"

            auth_dict['has_date_issue'] = has_date_issue
            auth_dict['date_issue_reason'] = date_issue_reason
            
            auth_dict['Auth_StartDate_formatted'] = auth_start_date_obj.strftime('%m/%d/%Y') if auth_start_date_obj else 'N/A'
            auth_dict['Auth_EndDate_formatted'] = auth_end_date_obj.strftime('%m/%d/%Y') if auth_end_date_obj else 'N/A'

            # Auth codes using same cursor
            if auth_dict.get('Auth_ID'):
                try:
                    cursor.execute("EXEC sp_AuthorizeCodeLink_GetByAuthID @Auth_ID = ?, @UnitsIn = ?", 
                                 auth_dict['Auth_ID'], 0)
                    auth_code_rows_raw = cursor.fetchall()
                    
                    auth_dict['auth_codes'] = [] 
                    for raw_code_row in auth_code_rows_raw:
                        code_name = (getattr(raw_code_row, 'CodeName', None) or 
                                     getattr(raw_code_row, 'Code_Name', None) or 
                                     getattr(raw_code_row, 'code_name', None) or '')
                        code_desc = (getattr(raw_code_row, 'CodeDescription', None) or 
                                     getattr(raw_code_row, 'Code_Desc', None) or 
                                     getattr(raw_code_row, 'code_desc', None) or 
                                     getattr(raw_code_row, 'Description', None) or '')
                        acl_start_str = getattr(raw_code_row, 'ACL_StartDate', None)
                        acl_end_str = getattr(raw_code_row, 'ACL_EndDate', None)
                        formatted_acl_start_date = 'N/A'
                        formatted_acl_end_date = 'N/A'
                        if acl_start_str and isinstance(acl_start_str, str):
                            try:
                                dt_obj = datetime.strptime(acl_start_str.split(' ')[0], '%Y-%m-%d') # Take only date part
                                formatted_acl_start_date = dt_obj.strftime('%m/%d/%Y')
                            except ValueError:
                                pass
                        elif acl_start_str and hasattr(acl_start_str, 'strftime'): # If it's already a date/datetime obj
                            formatted_acl_start_date = acl_start_str.strftime('%m/%d/%Y')
                        if acl_end_str and isinstance(acl_end_str, str):
                            try:
                                dt_obj = datetime.strptime(acl_end_str.split(' ')[0], '%Y-%m-%d') # Take only date part
                                formatted_acl_end_date = dt_obj.strftime('%m/%d/%Y')
                            except ValueError:
                                pass
                        elif acl_end_str and hasattr(acl_end_str, 'strftime'): # If it's already a date/datetime obj
                            formatted_acl_end_date = acl_end_str.strftime('%m/%d/%Y')

                        code_obj_dict = { 
                            'ACL_ID': getattr(raw_code_row, 'ACL_ID', None),
                            'Auth_ID': getattr(raw_code_row, 'Auth_ID', None), 
                            'CodeID': getattr(raw_code_row, 'CodeID', None),
                            'CodeName': code_name,
                            'CodeDescription': code_desc,
                            'ACL_WeeklyUnitsRequested': getattr(raw_code_row, 'ACL_WeeklyUnitsRequested', 0),
                            'ACL_WeeklyUnitsApproved': getattr(raw_code_row, 'ACL_WeeklyUnitsApproved', 0),
                            'ACL_WeeklyHoursRequested': getattr(raw_code_row, 'ACL_WeeklyHoursRequested', 0),
                            'ACL_WeeklyHoursApproved': getattr(raw_code_row, 'ACL_WeeklyHoursApproved', 0),
                            'ACL_TotalUnitsApproved': getattr(raw_code_row, 'ACL_TotalUnitsApproved', 0),
                            'ACL_UnitsRemaining': getattr(raw_code_row, 'ACL_UnitsRemaining', 0),
                            'ACL_StartDate': formatted_acl_start_date,
                            'ACL_EndDate': formatted_acl_end_date,
                            'ACL_Notes': getattr(raw_code_row, 'ACL_Notes', ''),
                            'ACL_AuthNumber': getattr(raw_code_row, 'ACL_AuthNumber', ''),
                            'HasPerAuthRule': getattr(raw_code_row, 'HasPerAuthRule', False),
                        }
                        auth_dict['auth_codes'].append(code_obj_dict)
                except Exception as e:
                    pass

            processed_auth_rows.append(auth_dict)
        
        return {
            'client_name': client_name,
            'authorizations': processed_auth_rows,
            'insurances': insurances_for_dropdown, 
            'auth_types': auth_types
        }
            
    except Exception as e:
        logger.error(f"Error in get_client_authorizations_data: {e}", exc_info=True)
        raise
    finally:
        # Always close the single connection
        if conn:
            conn.close()

@authorizations_bp.route('/client/<int:student_id>')
@login_required
@cache.cached(timeout=1800, key_prefix=make_cache_key)
@check_permission('authorizations.view')
def client_authorizations_detail(student_id):
    """Display authorizations for a specific client."""
    
    try:
        # Use the helper function to get all the data
        data = get_client_authorizations_data(student_id)
        
        # Get current user from session to check permissions
        user_id = session.get('user_id')
        current_user = User.query.get(user_id) if user_id else None
        
        # Determine what permissions the user has
        user_permissions = {
            'can_create': current_user.has_permission('authorizations.create') if current_user else False,
            'can_edit': current_user.has_permission('authorizations.edit') if current_user else False,
            'can_delete': current_user.has_permission('authorizations.delete') if current_user else False,
        }
        
        # Always return HTML
        return render_template('client_authorizations.html', 
                             student_id=student_id,
                             client_name=data['client_name'],
                             authorizations=data['authorizations'],
                             insurances=data['insurances'],
                             auth_types=data['auth_types'],
                             user_permissions=user_permissions)
                             
    except Exception as e:
        logger.error(f"Error loading client authorizations: {e}", exc_info=True)
        flash(f"Error loading authorizations: {str(e)}", "danger")
        return redirect(url_for('admin.index'))

@authorizations_bp.route('/client/<int:student_id>/api', methods=['GET'])
@login_required
@check_permission('authorizations.view')
def client_authorizations_api(student_id):
    """API endpoint that always returns JSON data for AJAX requests."""
    try:
        # Use the helper function to get all the data
        data = get_client_authorizations_data(student_id)
        
        # Process data for JSON response...
        authorizations_data = []
        for auth in data['authorizations']:
            auth_data = {
                'Auth_ID': auth['Auth_ID'],
                'StudentID': auth['StudentID'],
                'StudentInsLinkID': auth['StudentInsLinkID'],
                'InsuranceName': auth['InsuranceName'],
                'Auth_StartDate': auth['Auth_StartDate'].strftime('%Y-%m-%d') if auth['Auth_StartDate'] else None,
                'Auth_EndDate': auth['Auth_EndDate'].strftime('%Y-%m-%d') if auth['Auth_EndDate'] else None,
                'Auth_StartDate_formatted': auth['Auth_StartDate'].strftime('%m/%d/%Y') if auth['Auth_StartDate'] else 'N/A',
                'Auth_EndDate_formatted': auth['Auth_EndDate'].strftime('%m/%d/%Y') if auth['Auth_EndDate'] else 'N/A',
                'isCurrent': bool(auth['isCurrent']),
                'has_date_issue': auth['has_date_issue'],
                'date_issue_reason': auth['date_issue_reason'],
                'Auth_Notes': auth['Auth_Notes'],
                'Auth_Number': auth['Auth_Number'],
                'Auth_TotalMonthlyUnitsApproved': auth['Auth_TotalMonthlyUnitsApproved'],
                'MightAppeal': auth['MightAppeal'],
                'insurance_start_date': auth['insurance_start_date'].strftime('%m/%d/%Y') if hasattr(auth, 'insurance_start_date') and auth['insurance_start_date'] else 'N/A',
                'insurance_end_date': auth['insurance_end_date'].strftime('%m/%d/%Y') if hasattr(auth, 'insurance_end_date') and auth['insurance_end_date'] else 'N/A',
                'member_id': auth['member_id'],
                'group_id': auth['group_id'],
            }
            
            # Include auth codes if needed
            auth_codes = []
            if 'auth_codes' in auth:
                for code in auth['auth_codes']:
                    code_data = {
                        'CodeName': code['CodeName'],
                        'CodeDescription': code['CodeDescription'],
                        'ACL_StartDate': code['ACL_StartDate'],
                        'ACL_EndDate': code['ACL_EndDate'],
                        'ACL_WeeklyUnitsRequested': code['ACL_WeeklyUnitsRequested'],
                        'ACL_WeeklyUnitsApproved': code['ACL_WeeklyUnitsApproved'],
                        'ACL_WeeklyHoursRequested': code['ACL_WeeklyHoursRequested'],
                        'ACL_WeeklyHoursApproved': code['ACL_WeeklyHoursApproved'],
                        'ACL_TotalUnitsApproved': code['ACL_TotalUnitsApproved'],
                        'ACL_AuthNumber': code['ACL_AuthNumber'],
                        'HasPerAuthRule': code['HasPerAuthRule'],
                    }
                    auth_codes.append(code_data)
            auth_data['auth_codes'] = auth_codes
            
            authorizations_data.append(auth_data)
        
        return jsonify({
            'success': True,
            'client_name': data['client_name'],
            'authorizations': authorizations_data
        })
        
    except Exception as e:
        logger.error(f"Error in client authorizations API: {e}", exc_info=True)
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@authorizations_bp.route('/client/<int:student_id>/refresh', methods=['POST'])
@login_required
@check_permission('authorizations.view')
def refresh_client_authorizations(student_id):
    """Clear cache and return flag to fetch fresh data"""
    try:
        # First, clear the cache
        from flask import request, session
        
        # Get the key for the client_authorizations_detail endpoint
        user_id = session.get('user_id', 'anonymous')
        path = f"/authorizations/client/{student_id}"
        args = str(hash(frozenset(request.args.items())))
        cache_key = f"view/{user_id}{path}{args}"
        
        cache.delete(cache_key)
        
        # Return success response - client will make a separate request
        return jsonify({
            'success': True,
            'message': 'Cache cleared successfully',
            'fetch_fresh': True
        })
        
    except Exception as e:
        logger.error(f"Error refreshing client authorizations: {e}", exc_info=True)
        
        # Return error response
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500 

@authorizations_bp.route('/utilization/<int:auth_id>', methods=['GET'])
@login_required
@check_permission('authorizations.view')
def get_auth_utilization(auth_id):
    """Get authorization code utilization data."""
    try:
        logger.info(f"Getting utilization data for auth_id: {auth_id}")  # Debug log
        utilization_data = []
        auth_dates = {}
        
        with get_db_connection() as conn:
            cursor = conn.cursor()
            
            # Get authorization dates first
            cursor.execute("SELECT Auth_ID, Auth_StartDate, Auth_EndDate FROM tblAuthorizations WHERE Auth_ID = ?", (auth_id,))
            auth_row = cursor.fetchone()
            if auth_row:
                logger.info(f"Found auth row: Auth_ID={auth_row.Auth_ID}, Start={auth_row.Auth_StartDate}, End={auth_row.Auth_EndDate}")  # Debug log
                auth_dates = {
                    'start_date': auth_row.Auth_StartDate.strftime('%Y-%m-%d') if auth_row.Auth_StartDate else None,
                    'end_date': auth_row.Auth_EndDate.strftime('%Y-%m-%d') if auth_row.Auth_EndDate else None
                }
            else:
                logger.warning(f"No authorization found for auth_id: {auth_id}")  # Debug log
            
            # Get utilization data
            cursor.execute("EXEC sp_GetAuthCodeUtilization @AuthID = ?", (auth_id,))
            rows = cursor.fetchall()
            
            for row in rows:
                try:
                    # Use column name access instead of numerical indices
                    code_name = getattr(row, 'CodeName', None) or 'N/A'
                    total_approved = float(getattr(row, 'TotalApprovedUnits', 0) or 0)
                    units_utilized = float(getattr(row, 'UnitsUtilized', 0) or 0)
                    units_remaining = float(getattr(row, 'UnitsRemaining', 0) or 0)
                    
                    # Calculate utilization percentage
                    utilization_pct = (units_utilized / total_approved * 100) if total_approved > 0 else 0

                    utilization_data.append({
                        'CodeName': code_name,
                        'TotalApprovedUnits': total_approved,
                        'UnitsUtilized': units_utilized,
                        'UnitsRemaining': units_remaining,
                        'UtilizationPercent': round(utilization_pct, 2)
                    })
                    
                except Exception as row_error:
                    logger.error(f"Error processing utilization row for auth_id {auth_id}: {row_error}", exc_info=True)
                    continue
        
        logger.info(f"Returning auth_dates: {auth_dates}")  # Debug log
        return jsonify({
            'success': True,
            'data': utilization_data,
            'auth_id': auth_id,
            'auth_dates': auth_dates
        })
        
    except Exception as e:
        logger.error(f"Error getting authorization utilization for auth_id {auth_id}: {e}", exc_info=True)
        return jsonify({
            'success': False,
            'error': str(e),
            'auth_id': auth_id
        }), 500 