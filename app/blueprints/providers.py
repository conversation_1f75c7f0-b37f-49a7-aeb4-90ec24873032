import logging
import traceback
from datetime import datetime
from contextlib import contextmanager
import io
import csv

from flask import Blueprint, render_template, request, redirect, url_for, Response, flash

from app import cache
from app.auth import login_required
from app.database import get_read_only_connection
from app.middleware.permission_check import check_permission
from app.utils.encryption import decrypt_data

# Create the blueprint with a url_prefix
providers_bp = Blueprint('providers', __name__, url_prefix='/providers')

# Constants
QUERY_TIMEOUT_LIST = 15  # seconds for list queries
QUERY_TIMEOUT_DETAILS = 10  # seconds for detail queries
VALID_ROLES = ['4', '6']  # BCBA=4, BT=6
VALID_STATUSES = ['all', 'active', 'inactive']

# Helper Functions
def validate_filters(status_filter, role_filter):
    """Validate and normalize filter inputs"""
    # Validate status filter
    if status_filter not in VALID_STATUSES:
        status_filter = 'active'
    
    # Validate role filter
    if role_filter not in VALID_ROLES + [None, '']:
        role_filter = None
    elif role_filter == '':
        role_filter = None
    
    return status_filter, role_filter

def build_providers_query(status_filter, role_filter, location_filter, corporate_account_filter):
    """Build providers query with filters - shared by list and export functions"""
    base_query = """
        SELECT DISTINCT 
            vs.LoginID, vs.FirstName, vs.LastName, 
            vs.LastName + ', ' + vs.FirstName AS FullName,
            UPPER(LTRIM(RTRIM(vs.LastName))) AS LastNameTrimmed,
            UPPER(LTRIM(RTRIM(vs.FirstName))) AS FirstNameTrimmed,
            vs.EmailAddress, vs.R_Name AS RoleName,
            TRIM(ca.CA_Name) AS CorporateAccount,
            CASE 
                WHEN vs.isActive = 1 THEN 'Active' 
                ELSE 'Inactive' 
            END AS Status,
            TRIM(ol.OfficeLocState) AS Location
        FROM vwStaff vs
        LEFT JOIN tblCorporateAccounts ca ON vs.CorporateAccount = ca.CA_ID
        LEFT JOIN tblOfficeLocations ol ON vs.OfficeLocID = ol.OfficeLocID
        WHERE (vs.R_ID IN (4, 6) 
               OR CHARINDEX(',4,', ',' + ISNULL(LTRIM(RTRIM(vs.MultiRole)), '') + ',') > 0
               OR CHARINDEX(',6,', ',' + ISNULL(LTRIM(RTRIM(vs.MultiRole)), '') + ',') > 0)
    """
    
    params = []
    
    # Add status filter
    if status_filter == 'active':
        base_query += " AND vs.isActive = 1"
    elif status_filter == 'inactive':
        base_query += " AND vs.isActive = 0"
    
    # Add role filter (matches stored procedure logic)
    if role_filter:
        base_query += " AND (vs.R_ID = ? OR CHARINDEX(',' + ? + ',', ',' + ISNULL(LTRIM(RTRIM(vs.MultiRole)), '') + ',') > 0)"
        params.extend([role_filter, role_filter])
    
    # Add location filter
    if location_filter:
        base_query += " AND TRIM(ol.OfficeLocState) = ?"
        params.append(location_filter)
    
    # Add corporate account filter
    if corporate_account_filter:
        base_query += " AND TRIM(ca.CA_Name) = ?"
        params.append(corporate_account_filter)
    
    base_query += " ORDER BY LastNameTrimmed, FirstNameTrimmed"
    
    return base_query, params

def decrypt_field_safely(encrypted_data, field_name, provider_id):
    """Safely decrypt a field with proper error handling and logging"""
    if encrypted_data and isinstance(encrypted_data, bytes):
        try:
            return decrypt_data(encrypted_data)
        except Exception as e:
            logging.error(f"Error decrypting {field_name} for provider {provider_id}: {e}")
            return "Decryption Error"
    else:
        return encrypted_data or 'N/A'

def get_filter_data(conn):
    """Get locations and corporate accounts for filter dropdowns"""
    # Get locations
    locations_query = """
        SELECT DISTINCT TRIM(ol.OfficeLocState)
        FROM vwStaff vs
        LEFT JOIN tblOfficeLocations ol ON vs.OfficeLocID = ol.OfficeLocID
        WHERE ol.OfficeLocState IS NOT NULL
        AND (vs.R_ID IN (4, 6) 
             OR CHARINDEX(',4,', ',' + ISNULL(LTRIM(RTRIM(vs.MultiRole)), '') + ',') > 0
             OR CHARINDEX(',6,', ',' + ISNULL(LTRIM(RTRIM(vs.MultiRole)), '') + ',') > 0)
        ORDER BY 1
    """
    locations_result = conn.execute(locations_query).fetchall()
    locations = [l[0] for l in locations_result]
    
    # Get corporate accounts
    corporate_accounts_query = """
        SELECT DISTINCT TRIM(ca.CA_Name)
        FROM tblCorporateAccounts ca
        JOIN vwStaff vs ON ca.CA_ID = vs.CorporateAccount
        WHERE ca.CA_Name IS NOT NULL
        AND (vs.R_ID IN (4, 6) 
             OR CHARINDEX(',4,', ',' + ISNULL(LTRIM(RTRIM(vs.MultiRole)), '') + ',') > 0
             OR CHARINDEX(',6,', ',' + ISNULL(LTRIM(RTRIM(vs.MultiRole)), '') + ',') > 0)
        ORDER BY 1
    """
    corporate_accounts_result = conn.execute(corporate_accounts_query).fetchall()
    corporate_accounts = [ca[0] for ca in corporate_accounts_result]
    
    return locations, corporate_accounts

# Use the same context manager from clients.py for database connections
@contextmanager
def safe_db_connection(timeout=10, operation_name="database operation"):
    """
    Context manager to safely handle database connections with timeouts and proper cleanup.
    
    Args:
        timeout (int): Query timeout in seconds
        operation_name (str): Name of the operation for logging purposes
        
    Yields:
        Connection: The database connection object
    """
    conn = None
    try:
        # Get connection
        conn = get_read_only_connection()
        
        # Set timeout for queries (in seconds)
        conn.timeout = timeout
        
        logging.debug(f"Database connection established with {timeout}s timeout for {operation_name}")
        
        # Yield the connection for use in the with block
        yield conn
        
    except Exception as e:
        logging.error(f"Database error during {operation_name}: {str(e)}", exc_info=True)
        raise
    finally:
        # Always ensure connection is closed
        if conn:
            try:
                conn.close()
                logging.debug(f"Database connection closed after {operation_name}")
            except Exception as close_error:
                logging.error(f"Error closing database connection after {operation_name}: {str(close_error)}")


@providers_bp.route('/company-providers')
@login_required
@cache.cached(timeout=3600, make_cache_key=lambda *args, **kwargs: f'providers_list_{request.args.get("status", "active")}_{request.args.get("role", "")}_{request.args.get("location", "")}_{request.args.get("corporateAccount", "")}')
@check_permission('provider.view')
def company_providers():
    """Company providers dashboard."""
    # Get and validate filters
    status_filter, role_filter = validate_filters(
        request.args.get('status', 'active'), 
        request.args.get('role')
    )
    location_filter = request.args.get('location')
    corporate_account_filter = request.args.get('corporateAccount')

    try:
        with safe_db_connection(QUERY_TIMEOUT_LIST, "company_providers") as conn:
            # Get filter data for dropdowns
            locations, corporate_accounts = get_filter_data(conn)
            
            # Build and execute main query
            query_sql, params = build_providers_query(
                status_filter, role_filter, location_filter, corporate_account_filter
            )
            
            providers_result = conn.execute(query_sql, tuple(params))
            column_names = [desc[0] for desc in providers_result.description]
            providers = [dict(zip(column_names, row)) for row in providers_result.fetchall()]
            
            logging.info(f"Fetched {len(providers)} providers with filters: status={status_filter}, role={role_filter}")

        return render_template('providers_list.html',
                               providers=providers,
                               locations=locations,
                               corporate_accounts=corporate_accounts,
                               current_status=status_filter,
                               current_role=role_filter,
                               current_location=location_filter,
                               current_corporate_account=corporate_account_filter,
                               error=None)

    except Exception as e:
        error_message = f"Error loading company provider data: {str(e)}"
        logging.error(f"Provider list error: {error_message}")
        logging.error(f"Traceback: {traceback.format_exc()}")
        
        return render_template('providers_list.html',
                               error=error_message,
                               providers=[],
                               locations=[],
                               corporate_accounts=[],
                               current_status=status_filter,
                               current_role=role_filter,
                               current_location=location_filter,
                               current_corporate_account=corporate_account_filter)


@providers_bp.route('/provider-details/<int:provider_id>')
@login_required
@cache.cached(timeout=3600, make_cache_key=lambda *args, **kwargs: f'provider_details_{request.view_args.get("provider_id") if request.view_args else "none"}')
@check_permission('provider.view')
def provider_details(provider_id):
    """View detailed provider information including encrypted data."""
    try:
        with safe_db_connection(QUERY_TIMEOUT_DETAILS, "provider_details") as conn:
            # Get detailed provider info
            provider_query = """
                SELECT 
                    vs.LoginID, vs.FirstName, vs.MiddleName, vs.LastName,
                    vs.S_legalFirst AS LegalFirstName, vs.S_legalLast AS LegalLastName,
                    vs.MiddleName AS LegalMiddleName, vs.KnownAs, vs.Languages,
                    vs.PreferredLanguage, pl.langname AS PreferredLanguageName,
                    vs.DOB, vs.Gender, vs.EmailAddress, vs.UserName, vs.PwdLastSet,
                    u.AlternateEmail, vs.StreetAddress, vs.City, vs.St AS State,
                    vs.zip AS ZipCode, vs.CellPhone AS Cellphone, pn.PN_Notes AS PhoneNotes,
                    pt.PT_Desc AS PhoneType, vs.NPI, vs.SSN, vs.NameOnCheck,
                    vs.PayrollNum, vs.PayType, pte.name AS PaymentTypeName,
                    vs.PLInsurance, vs.PLPolicyNumber, vs.PLStartDate, vs.PLEndDate,
                    vs.CAQH_ID, vs.CAQH_UserName, vs.CAQH_Password, vs.CAQH_Notes,
                    vs.CAQH_Expire, vs.R_Name AS RoleName, vs.R_ID AS RoleID,
                    TRIM(ca.CA_Name) AS CorporateAccount,
                    CASE WHEN vs.isActive = 1 THEN 'Active' ELSE 'Inactive' END AS Status,
                    si.DateTerminated, si.DateCreated AS ContractStartDate,
                    si.DateModified AS ContractEndDate, TRIM(ol.OfficeLocName) AS Location,
                    TRIM(ol.OfficeLocState) AS LocationState
                FROM vwStaff vs
                LEFT JOIN Users u ON vs.LoginID = u.UserID
                LEFT JOIN tblPhoneNumbers pn ON vs.LoginID = pn.PN_UserID
                LEFT JOIN tblPhoneTypes pt ON pn.PN_TypeOfPhone = pt.PT_ID
                LEFT JOIN StaffIntake si ON vs.LoginID = si.SI_LoginID
                LEFT JOIN tblCorporateAccounts ca ON vs.CorporateAccount = ca.CA_ID
                LEFT JOIN tblOfficeLocations ol ON vs.OfficeLocID = ol.OfficeLocID
                LEFT JOIN lang pl ON vs.PreferredLanguage = pl.lang_id
                LEFT JOIN PayTypes_enm pte ON vs.PayType = pte.id
                WHERE vs.LoginID = ? AND (vs.R_ID IN (4, 6) 
                       OR CHARINDEX(',4,', ',' + ISNULL(LTRIM(RTRIM(vs.MultiRole)), '') + ',') > 0
                       OR CHARINDEX(',6,', ',' + ISNULL(LTRIM(RTRIM(vs.MultiRole)), '') + ',') > 0)
            """

            cursor = conn.execute(provider_query, (provider_id,))
            provider_result = cursor.fetchone()
            
            if not provider_result:
                cursor.close()
                logging.info(f"No provider found for LoginID: {provider_id}")
                return render_template('provider_details.html',
                                       error="Provider not found",
                                       provider=None,
                                       credentials=[], licenses=[], pay_rates=[])

            # Convert to dictionary
            column_names = [desc[0] for desc in cursor.description]
            provider = dict(zip(column_names, provider_result))
            cursor.close()

            # Get additional data for BCBAs and all providers
            credentials, licenses, pay_rates = _get_provider_additional_data(conn, provider_id, provider.get('RoleID'))
            
            # Process and decrypt sensitive data
            _process_provider_data(provider, provider_id, conn)

        return render_template('provider_details.html', 
                             provider=provider, 
                             credentials=credentials, 
                             licenses=licenses, 
                             pay_rates=pay_rates)

    except Exception as e:
        error_msg = f"Error loading provider details for provider_id {provider_id}: {str(e)}"
        logging.error(f"Provider details error: {error_msg}")
        logging.error(f"Traceback: {traceback.format_exc()}")
        
        return render_template('provider_details.html', 
                             error=f"An error occurred loading provider details: {str(e)}", 
                             provider=None, 
                             credentials=[], licenses=[], pay_rates=[])

def _get_provider_additional_data(conn, provider_id, role_id):
    """Get credentials, licenses, and pay rates for a provider"""
    credentials = []
    licenses = []
    
    # Get credentials and licenses for BCBAs only
    if role_id == 4:  # BCBA
        # Credentials
        credentials_query = """
            SELECT pc.ProvCredID, pc.CredentialsID, ins.Ins_Name AS InsuranceName,
                   pc.Ins_ID, pc.ProvCred_StartDate, pc.RecertificationDate, pc.Notes,
                   pc.AffiliatedDate, pc.EtinLinked, pc.IRNumber, pc.IRStartDate
            FROM tblProviderCredentials pc
            LEFT JOIN tblInsurances ins ON pc.Ins_ID = ins.Ins_ID
            WHERE pc.ProviderID = ?
            ORDER BY pc.EtinLinked DESC, ins.Ins_Name
        """
        credentials_cursor = conn.execute(credentials_query, (provider_id,))
        credentials_rows = credentials_cursor.fetchall()
        credentials = [dict(zip([desc[0] for desc in credentials_cursor.description], row)) 
                      for row in credentials_rows]
        credentials_cursor.close()

        # Licenses
        licenses_query = """
            SELECT ProvLic_Type, ProvLic_Number, ProvLic_State,
                   ProvLic_StartDate, ProvLic_EndDate
            FROM tblProviderLicenses
            WHERE ProviderID = ?
            ORDER BY ProvLic_State, ProvLic_Type
        """
        licenses_cursor = conn.execute(licenses_query, (provider_id,))
        licenses_rows = licenses_cursor.fetchall()
        licenses = [dict(zip([desc[0] for desc in licenses_cursor.description], row)) 
                   for row in licenses_rows]
        licenses_cursor.close()

    # Get pay rates for all providers
    pay_rates_query = """
        SELECT PayRateType, amount AS PayRateAmount,
               startdate AS PayRateStartDate, endDate AS PayRateEndDate
        FROM tblPayRate
        WHERE StaffID = ?
        ORDER BY startdate DESC
    """
    pay_rates_cursor = conn.execute(pay_rates_query, (provider_id,))
    pay_rates_rows = pay_rates_cursor.fetchall()
    pay_rates = [dict(zip([desc[0] for desc in pay_rates_cursor.description], row)) 
                for row in pay_rates_rows]
    pay_rates_cursor.close()

    return credentials, licenses, pay_rates

def _process_provider_data(provider, provider_id, conn):
    """Process languages and decrypt sensitive provider data"""
    # Process Languages
    if provider.get('Languages'):
        try:
            language_ids = [id.strip() for id in str(provider['Languages']).split(',') if id.strip()]
            if language_ids:
                placeholders = ','.join(['?' for _ in language_ids])
                lang_query = f"SELECT langname FROM lang WHERE lang_id IN ({placeholders})"
                lang_result = conn.execute(lang_query, language_ids).fetchall()
                language_names = [row[0] for row in lang_result]
                provider['LanguageNames'] = ', '.join(language_names) if language_names else 'N/A'
            else:
                provider['LanguageNames'] = 'N/A'
        except Exception as lang_error:
            logging.error(f"Error processing languages for provider {provider_id}: {lang_error}")
            provider['LanguageNames'] = 'Error loading languages'
    else:
        provider['LanguageNames'] = 'N/A'

    # Decrypt and format DOB
    encrypted_dob = provider.get('DOB')
    if encrypted_dob and isinstance(encrypted_dob, bytes):
        try:
            decrypted_dob_str = decrypt_data(encrypted_dob)
            if decrypted_dob_str:
                dob_date_str = decrypted_dob_str.strip().split(' ')[0]
                dob_date = None
                for fmt in ('%Y-%m-%d', '%m/%d/%Y', '%Y%m%d'):
                    try:
                        dob_date = datetime.strptime(dob_date_str, fmt).date()
                        break
                    except ValueError:
                        continue
                provider['DOB_formatted'] = dob_date.strftime('%m/%d/%Y') if dob_date else f"Could not parse date: {dob_date_str}"
            else:
                provider['DOB_formatted'] = "Decryption Failed"
        except Exception as decrypt_err:
            provider['DOB_formatted'] = "Decryption Error"
            logging.error(f"Error decrypting DOB for provider {provider_id}: {decrypt_err}")
    else:
        provider['DOB_formatted'] = str(encrypted_dob) if encrypted_dob else 'N/A'

    # Construct full address
    address_parts = [provider.get('StreetAddress'), provider.get('City'), 
                     provider.get('State'), provider.get('ZipCode')]
    provider['FullAddress'] = ", ".join(filter(None, address_parts)) or 'N/A'

    # Decrypt BCBA-specific fields
    if provider.get('RoleID') == 4:  # BCBA
        provider['CAQH_ID_decrypted'] = decrypt_field_safely(provider.get('CAQH_ID'), 'CAQH_ID', provider_id)
        provider['CAQH_Password_decrypted'] = decrypt_field_safely(provider.get('CAQH_Password'), 'CAQH_Password', provider_id)

    # Decrypt SSN for all providers
    provider['SSN_decrypted'] = decrypt_field_safely(provider.get('SSN'), 'SSN', provider_id)


@providers_bp.route('/export-company-providers')
@login_required
@cache.cached(timeout=3600, make_cache_key=lambda *args, **kwargs: f'providers_export_{request.args.get("status", "active")}_{request.args.get("role", "")}_{request.args.get("location", "")}_{request.args.get("corporateAccount", "")}')
@check_permission('provider.view')
def export_company_providers():
    """Export company providers data to CSV."""
    # Get and validate filters
    status_filter, role_filter = validate_filters(
        request.args.get('status', 'active'), 
        request.args.get('role')
    )
    location_filter = request.args.get('location')
    corporate_account_filter = request.args.get('corporateAccount')

    try:
        with safe_db_connection(QUERY_TIMEOUT_LIST, "export_providers") as conn:
            # Build and execute query (reuse same logic as list)
            query_sql, params = build_providers_query(
                status_filter, role_filter, location_filter, corporate_account_filter
            )
            
            result = conn.execute(query_sql, tuple(params))
            results = result.fetchall()
            column_names = [column[0] for column in result.description]
            
            logging.info(f"Exporting {len(results)} providers with filters: status={status_filter}, role={role_filter}")

        # Generate CSV
        output = io.StringIO()
        writer = csv.writer(output)
        writer.writerow(column_names)
        for row in results:
            writer.writerow(row)

        # Generate filename
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename_parts = ["company_providers", status_filter]
        
        if role_filter: 
            role_name = "BCBA" if role_filter == "4" else "BT"
            filename_parts.append(f"role_{role_name}")
        if location_filter: 
            filename_parts.append(f"loc_{location_filter.replace(' ', '_')}")
        if corporate_account_filter: 
            safe_corp_name = corporate_account_filter.replace(' ', '_').replace('/', '-')
            filename_parts.append(f"corp_{safe_corp_name}")
        
        filename_parts.append(timestamp)
        filename = "_".join(filename_parts) + ".csv"

        return Response(
            output.getvalue(),
            mimetype="text/csv",
            headers={"Content-Disposition": f"attachment;filename={filename}"}
        )

    except Exception as e:
        error_message = f"Error exporting company provider data: {str(e)}"
        logging.error(f"Export failed: {error_message}")
        logging.error(f"Traceback: {traceback.format_exc()}")
        
        return render_template('providers_list.html',
                               error=f"Could not export data: {error_message}",
                               providers=[],
                               locations=[],
                               corporate_accounts=[],
                               current_status=status_filter,
                               current_role=role_filter,
                               current_location=location_filter,
                               current_corporate_account=corporate_account_filter)