<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Stagewise Toolbar Test</title>
  <link rel="stylesheet" href="/static/js/react-root-components/assets/main.css">
</head>
<body>
  <h1>Stagewise Toolbar Test Page</h1>
  <div id="react-dashboard-mount"></div>
  
  <script>
    // Set up test config
    window.dashboardConfig = {
      userId: 1,
      isCaseManager: true
    };
    
    // Set dev mode to ensure stagewise loads
    window.VITE_STAGEWISE = 'true';
    
    // Add console interceptor to see all logs
    const originalLog = console.log;
    const originalWarn = console.warn;
    const originalError = console.error;
    
    const logOutput = document.createElement('pre');
    logOutput.style.cssText = 'background: #000; color: #0f0; padding: 20px; margin: 20px; font-family: monospace;';
    document.body.appendChild(logOutput);
    
    function addLog(type, ...args) {
      const msg = `[${type}] ${args.map(a => typeof a === 'object' ? JSON.stringify(a, null, 2) : String(a)).join(' ')}\n`;
      logOutput.textContent += msg;
    }
    
    console.log = (...args) => { originalLog(...args); addLog('LOG', ...args); };
    console.warn = (...args) => { originalWarn(...args); addLog('WARN', ...args); };
    console.error = (...args) => { originalError(...args); addLog('ERROR', ...args); };
  </script>
  
  <script type="module" src="/static/js/react-root-components/assets/main.js"></script>
</body>
</html>