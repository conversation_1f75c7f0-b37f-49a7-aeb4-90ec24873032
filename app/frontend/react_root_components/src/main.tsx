import React from "react";
import { createRoot } from "react-dom/client";
import "./index.css";
import { ReactPlugin } from "@stagewise-plugins/react";
import { StagewiseToolbar } from "@stagewise/toolbar-react";

// Import the Dashboard component which handles all the interactivity
import Dashboard from "./pages/Dashboard";
import PhoneLogPage from "./pages/PhoneLogPage";
import { Toaster } from "@/components/ui/toaster";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";

// Mount points for AdminLTE integration
document.addEventListener("DOMContentLoaded", () => {
  console.log("🚀 main.tsx is executing!");

  // Mount the full Dashboard component if the mount point exists
  const dashboardMount = document.getElementById("react-dashboard-mount");
  if (dashboardMount) {
    const dashboardRoot = createRoot(dashboardMount);

    // Get user config from window object (set by AdminLTE template)
    const config = window.dashboardConfig || {
      userId: 1,
      isCaseManager: true,
    };

    // Store config globally for Dashboard to use
    window.dashboardConfig = config;

    dashboardRoot.render(<Dashboard />);
  }

  // Mount the PhoneLogPage component if the mount point exists
  const phoneLogMount = document.getElementById("react-phone-log-mount");
  if (phoneLogMount) {
    console.log("📱 Mounting PhoneLogPage component");
    const phoneLogRoot = createRoot(phoneLogMount);
    const queryClient = new QueryClient();

    phoneLogRoot.render(
      <React.StrictMode>
        <QueryClientProvider client={queryClient}>
          <PhoneLogPage />
          <Toaster />
        </QueryClientProvider>
      </React.StrictMode>
    );
    console.log("✅ PhoneLogPage mounted successfully");
  }
  // Note: No error logging when mount point not found - this is expected on non-phone-log pages

  console.log("✅ React dashboard ready for AdminLTE mounting");

  // Stagewise toolbar (development only or if explicitly enabled)
  const showStagewise = true; // Force enable for debugging
  console.log("🛠️ Stagewise toolbar check:", {
    DEV: import.meta.env.DEV,
    VITE_STAGEWISE: import.meta.env.VITE_STAGEWISE,
    showStagewise,
    forced: true,
  });
  if (showStagewise) {
    try {
      const stagewiseConfig = {
        plugins: [ReactPlugin],
      };

      const toolbarContainer = document.createElement("div");
      toolbarContainer.id = "stagewise-toolbar-root";
      // Add visible styles for debugging
      toolbarContainer.style.cssText =
        "position: fixed; top: 0; right: 0; z-index: 99999; background: rgba(0,0,255,0.1); min-width: 300px; min-height: 50px; border: 2px solid red;";
      document.body.appendChild(toolbarContainer);
      console.log("📦 Toolbar container created:", toolbarContainer);

      const toolbarRoot = createRoot(toolbarContainer);
      console.log("🌳 React root created:", toolbarRoot);

      // Add a test div first to ensure React is working
      toolbarRoot.render(
        <div style={{ padding: "10px", background: "yellow" }}>
          Stagewise Loading...
        </div>
      );

      // Then try the actual toolbar
      setTimeout(() => {
        console.log("🎯 Attempting to render StagewiseToolbar...");
        toolbarRoot.render(<StagewiseToolbar config={stagewiseConfig} />);
        console.log("✅ Stagewise toolbar rendered successfully");
      }, 1000);
    } catch (error) {
      console.warn("❌ Failed to load stagewise toolbar:", error);
    }
  }
});
