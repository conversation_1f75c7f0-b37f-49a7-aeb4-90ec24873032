import React, { useEffect, useState } from 'react';
import { createRoot } from 'react-dom/client';
import { ReactPlugin } from '@stagewise-plugins/react';
import { StagewiseToolbar } from '@stagewise/toolbar-react';

function TestComponent() {
  const [logs, setLogs] = useState<string[]>([]);
  const [toolbarRoot, setToolbarRoot] = useState<HTMLElement | null>(null);

  useEffect(() => {
    const addLog = (msg: string) => {
      setLogs(prev => [...prev, `${new Date().toISOString()}: ${msg}`]);
      console.log(msg);
    };

    addLog('TestComponent mounted');
    
    // Check if toolbar root exists
    const root = document.getElementById('stagewise-toolbar-root');
    if (root) {
      addLog(`Found toolbar root: ${root.id}, dimensions: ${root.offsetWidth}x${root.offsetHeight}`);
      setToolbarRoot(root);
      
      // Check computed styles
      const styles = window.getComputedStyle(root);
      addLog(`Toolbar root styles - display: ${styles.display}, visibility: ${styles.visibility}, opacity: ${styles.opacity}`);
      
      // Check children
      addLog(`Toolbar root children count: ${root.children.length}`);
      if (root.children.length > 0) {
        Array.from(root.children).forEach((child, i) => {
          const childStyles = window.getComputedStyle(child as HTMLElement);
          addLog(`Child ${i}: ${child.tagName}, display: ${childStyles.display}, dimensions: ${(child as HTMLElement).offsetWidth}x${(child as HTMLElement).offsetHeight}`);
        });
      }
    } else {
      addLog('Toolbar root not found, creating it...');
      const container = document.createElement('div');
      container.id = 'stagewise-toolbar-root';
      container.style.cssText = 'position: fixed; top: 0; right: 0; z-index: 9999; background: rgba(255,0,0,0.1); min-width: 300px; min-height: 50px;';
      document.body.appendChild(container);
      setToolbarRoot(container);
    }

    // Look for any stagewise elements
    const allElements = document.querySelectorAll('[class*="stagewise"], [id*="stagewise"]');
    addLog(`Found ${allElements.length} elements with stagewise in class/id`);
    allElements.forEach((el, i) => {
      addLog(`Element ${i}: ${el.tagName}#${el.id}.${el.className}`);
    });
  }, []);

  return (
    <div style={{ padding: '20px', fontFamily: 'monospace' }}>
      <h2>Stagewise Toolbar Test</h2>
      <div style={{ marginBottom: '20px' }}>
        <h3>Toolbar Root Info:</h3>
        {toolbarRoot ? (
          <div>
            <p>ID: {toolbarRoot.id}</p>
            <p>Dimensions: {toolbarRoot.offsetWidth}x{toolbarRoot.offsetHeight}</p>
            <p>Children: {toolbarRoot.children.length}</p>
          </div>
        ) : (
          <p>No toolbar root found</p>
        )}
      </div>
      <div>
        <h3>Debug Logs:</h3>
        <pre style={{ background: '#f0f0f0', padding: '10px', maxHeight: '400px', overflow: 'auto' }}>
          {logs.join('\n')}
        </pre>
      </div>
      <div style={{ marginTop: '20px' }}>
        <button 
          onClick={() => {
            if (toolbarRoot) {
              const stagewiseConfig = { plugins: [ReactPlugin] };
              const root = createRoot(toolbarRoot);
              try {
                root.render(<StagewiseToolbar config={stagewiseConfig} />);
                setLogs(prev => [...prev, `${new Date().toISOString()}: Manually rendered StagewiseToolbar`]);
              } catch (error) {
                setLogs(prev => [...prev, `${new Date().toISOString()}: Error rendering toolbar: ${error}`]);
              }
            }
          }}
        >
          Manually Render Toolbar
        </button>
      </div>
    </div>
  );
}

// Mount test component
const mount = document.getElementById('test-mount');
if (mount) {
  const root = createRoot(mount);
  root.render(<TestComponent />);
}