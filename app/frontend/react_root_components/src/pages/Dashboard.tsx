
import { useState, useCallback, useEffect } from "react";
import { StatCard } from "@/components/cards/StatCard";
import { ParentCommunicationTable } from "@/components/communication";
import { ComingSoon } from "@/components/layout/ComingSoon";
import { Bell, Phone, User, FileText, Calendar, CheckSquare, Shield, Users, ClipboardList } from "lucide-react";
import { statCardService, type StatCardData } from "@/services/statCardService";
import { phoneLogService, PendingStudent } from "@/services/phoneLogService";
import { useCommunicationStore } from "@/stores/communicationStore";
import { Toaster } from "@/components/ui/toaster";

type CardId = 
  | "notifications"
  | "parent-communications" 
  | "insurance-checks"
  | "client-caseload"
  | "auth-expiring"
  | "auth-expired"
  | "pending-staff"
  | "pending-assignments"
  | "provider-missing-info";

interface CardState {
  data: StatCardData | null;
  loading: boolean;
  lastFetched: number | null;
}

const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

// Mock data for cards - will be replaced one by one with real API calls
const MOCK_DATA: Record<CardId, number> = {
  "notifications": 10,
  "parent-communications": 5,
  "insurance-checks": 3,
  "client-caseload": 1,
  "auth-expiring": 10,
  "auth-expired": 5,
  "pending-staff": 3,
  "pending-assignments": 1,
  "provider-missing-info": 10,
};

const Dashboard = () => {
  const [selectedCard, setSelectedCard] = useState<CardId | null>("parent-communications");
  const [cardStates, setCardStates] = useState<Record<CardId, CardState>>({
    "notifications": { data: null, loading: false, lastFetched: null },
    "parent-communications": { data: null, loading: false, lastFetched: null },
    "insurance-checks": { data: null, loading: false, lastFetched: null },
    "client-caseload": { data: null, loading: false, lastFetched: null },
    "auth-expiring": { data: null, loading: false, lastFetched: null },
    "auth-expired": { data: null, loading: false, lastFetched: null },
    "pending-staff": { data: null, loading: false, lastFetched: null },
    "pending-assignments": { data: null, loading: false, lastFetched: null },
    "provider-missing-info": { data: null, loading: false, lastFetched: null },
  });
  const [animatingCount, setAnimatingCount] = useState(false);
  const [phoneLogData, setPhoneLogData] = useState<any[]>([]);

  const loadCardData = useCallback(async (cardId: CardId, force = false) => {
    const currentState = cardStates[cardId];
    const now = Date.now();
    
    // Check if we have fresh data (within cache duration) and not forcing
    if (!force && currentState.data && currentState.lastFetched && 
        (now - currentState.lastFetched) < CACHE_DURATION) {
      return;
    }

    // Set loading state
    setCardStates(prev => ({
      ...prev,
      [cardId]: { ...prev[cardId], loading: true }
    }));

    try {
      // Special handling for parent-communications to use phoneLogService
      if (cardId === 'parent-communications') {
        const response = await phoneLogService.getPendingCalls();
        
        // Transform the data to match the expected format
        const transformedStudents = response.data.map((student: PendingStudent) => {
          // Get the first parent contact for display
          const primaryContact = student.parent_contacts[0] || { name: 'Unknown', phone: 'No phone' };
          
          // Determine status based on priority and days_since_last_call
          let status: 'Never Called' | 'Pending' | 'Overdue' | 'Called' = 'Pending';
          if (student.priority === 'never_called') {
            status = 'Never Called';
          } else if (student.priority === 'overdue' || (student.days_since_last_call && student.days_since_last_call > 30)) {
            status = 'Overdue';
          }

          return {
            id: student.student_id,
            name: student.name,
            guardianName: primaryContact.name,
            guardianPhone: primaryContact.phone,
            lastContactDate: student.last_call_date,
            status,
            daysOverdue: student.days_since_last_call
          };
        });

        setPhoneLogData(transformedStudents);
        
        setCardStates(prev => ({
          ...prev,
          [cardId]: {
            data: {
              count: response.data.length,
              details: transformedStudents,
              lastUpdated: new Date().toISOString()
            },
            loading: false,
            lastFetched: now
          }
        }));
      } else {
        // Use the default statCardService for other cards
        const dataFetcher = statCardService.getDataFetcher(cardId);
        const data = await dataFetcher();
        
        setCardStates(prev => ({
          ...prev,
          [cardId]: {
            data,
            loading: false,
            lastFetched: now
          }
        }));
      }
    } catch (error) {
      console.error(`Error loading data for ${cardId}:`, error);
      setCardStates(prev => ({
        ...prev,
        [cardId]: {
          data: { 
            count: 0, 
            error: error instanceof Error ? error.message : 'Failed to load data' 
          },
          loading: false,
          lastFetched: now
        }
      }));
    }
  }, [cardStates]);

  const handleCardClick = useCallback(async (cardId: CardId) => {
    setSelectedCard(cardId);
    // Only load data for parent-communications card for now
    if (cardId === "parent-communications") {
      await loadCardData(cardId);
    }
  }, [loadCardData]);

  // Auto-fetch data for selected card on page load
  useEffect(() => {
    if (selectedCard === "parent-communications") {
      loadCardData(selectedCard);
    }
  }, []); // Empty dependency array means this runs only on mount

  // Refresh data when page becomes visible (user returns from phone log)
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (!document.hidden && selectedCard === "parent-communications") {
        // Force refresh when page becomes visible again
        loadCardData(selectedCard, true);
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [selectedCard, loadCardData]);

  // Listen for parent communication completion events
  useEffect(() => {
    const handleParentCommCompleted = (event: CustomEvent) => {
      // Decrease the count for parent-communications card
      setCardStates(prev => {
        const currentState = prev["parent-communications"];
        if (currentState.data && currentState.data.count > 0) {
          // Trigger animation
          setAnimatingCount(true);
          setTimeout(() => setAnimatingCount(false), 300);
          
          return {
            ...prev,
            "parent-communications": {
              ...currentState,
              data: {
                ...currentState.data,
                count: currentState.data.count - 1
              }
            }
          };
        }
        return prev;
      });
    };

    window.addEventListener('parentCommCompleted', handleParentCommCompleted as EventListener);
    return () => {
      window.removeEventListener('parentCommCompleted', handleParentCommCompleted as EventListener);
    };
  }, []);

  const renderContent = () => {
    switch (selectedCard) {
      case "parent-communications":
        const parentCommState = cardStates["parent-communications"];
        const students = parentCommState.data?.details || phoneLogData;
        return <ParentCommunicationTable 
          userId={1} 
          isCaseManager={true} 
          students={students}
          loading={parentCommState.loading}
          error={parentCommState.data?.error}
        />;
      case "notifications":
        return <ComingSoon title="Notifications Center" subtitle="Real-time notifications and alerts coming soon" icon={<Bell className="h-8 w-8" />} />;
      case "insurance-checks":
        return <ComingSoon title="Insurance Verification" subtitle="Monthly insurance checks dashboard coming soon" icon={<Shield className="h-8 w-8" />} />;
      case "client-caseload":
        return <ComingSoon title="Client Caseload" subtitle="Detailed caseload management coming soon" icon={<Users className="h-8 w-8" />} />;
      case "auth-expiring":
        return <ComingSoon title="Authorization Alerts" subtitle="Expiring authorization tracking coming soon" icon={<Calendar className="h-8 w-8" />} />;
      case "auth-expired":
        return <ComingSoon title="Expired Authorizations" subtitle="Expired authorization management coming soon" icon={<FileText className="h-8 w-8" />} />;
      case "pending-staff":
        return <ComingSoon title="Staff Assignment" subtitle="Client staffing workflow coming soon" icon={<User className="h-8 w-8" />} />;
      case "pending-assignments":
        return <ComingSoon title="Assignment Queue" subtitle="Client assignment management coming soon" icon={<ClipboardList className="h-8 w-8" />} />;
      case "provider-missing-info":
        return <ComingSoon title="Provider Information" subtitle="Provider data management coming soon" icon={<FileText className="h-8 w-8" />} />;
      default:
        return <ComingSoon />;
    }
  };

  // Helper function to get display value for a card
  const getCardValue = (cardId: CardId) => {
    const state = cardStates[cardId];
    
    // If we have real data loaded, use it
    if (state.data && !state.data.error) {
      return state.data.count?.toString() || MOCK_DATA[cardId].toString();
    }
    
    // Otherwise use mock data
    return MOCK_DATA[cardId].toString();
  };

  // Helper function to check if card has fresh data
  const hasCardData = (cardId: CardId) => {
    return cardStates[cardId].data && !cardStates[cardId].data.error;
  };

  return (
    <div className="space-y-6">
      <h1 className="text-3xl font-bold tracking-tight">Dashboard</h1>
      
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <StatCard 
          id="notifications"
          title="Notifications" 
          value={getCardValue("notifications")} 
          icon={<Bell className="h-6 w-6" />} 
          iconClassName="bg-amber-100 text-amber-500"
          onClick={() => handleCardClick("notifications")}
          selected={selectedCard === "notifications"}
          loading={cardStates["notifications"].loading}
          error={cardStates["notifications"].data?.error}
          showDataFetched={hasCardData("notifications")}
        />
        <StatCard 
          id="parent-communications"
          title="Parent/Guardian Communications" 
          value={getCardValue("parent-communications")} 
          icon={<Phone className="h-6 w-6" />} 
          iconClassName="bg-green-100 text-green-500"
          onClick={() => handleCardClick("parent-communications")}
          selected={selectedCard === "parent-communications"}
          loading={cardStates["parent-communications"].loading}
          error={cardStates["parent-communications"].data?.error}
          showDataFetched={hasCardData("parent-communications")}
          animating={animatingCount && selectedCard === "parent-communications"}
        />
        <StatCard 
          id="insurance-checks"
          title="Insurance Checks per Month" 
          value={getCardValue("insurance-checks")} 
          icon={<CheckSquare className="h-6 w-6" />} 
          iconClassName="bg-orange-100 text-orange-500"
          onClick={() => handleCardClick("insurance-checks")}
          selected={selectedCard === "insurance-checks"}
          loading={cardStates["insurance-checks"].loading}
          error={cardStates["insurance-checks"].data?.error}
          showDataFetched={hasCardData("insurance-checks")}
        />
        <StatCard 
          id="client-caseload"
          title="Client Caseload" 
          value={getCardValue("client-caseload")} 
          icon={<User className="h-6 w-6" />} 
          iconClassName="bg-purple-100 text-purple-500"
          onClick={() => handleCardClick("client-caseload")}
          selected={selectedCard === "client-caseload"}
          loading={cardStates["client-caseload"].loading}
          error={cardStates["client-caseload"].data?.error}
          showDataFetched={hasCardData("client-caseload")}
        />

        <StatCard 
          id="auth-expiring"
          title="Authorization Expiring" 
          value={getCardValue("auth-expiring")} 
          icon={<Calendar className="h-6 w-6" />} 
          iconClassName="bg-pink-100 text-pink-500"
          onClick={() => handleCardClick("auth-expiring")}
          selected={selectedCard === "auth-expiring"}
          loading={cardStates["auth-expiring"].loading}
          error={cardStates["auth-expiring"].data?.error}
          showDataFetched={hasCardData("auth-expiring")}
        />
        <StatCard 
          id="auth-expired"
          title="Authorization Expired" 
          value={getCardValue("auth-expired")} 
          icon={<FileText className="h-6 w-6" />} 
          iconClassName="bg-lime-100 text-lime-600"
          onClick={() => handleCardClick("auth-expired")}
          selected={selectedCard === "auth-expired"}
          loading={cardStates["auth-expired"].loading}
          error={cardStates["auth-expired"].data?.error}
          showDataFetched={hasCardData("auth-expired")}
        />
        <StatCard 
          id="pending-staff"
          title="Clients Pending Staff" 
          value={getCardValue("pending-staff")} 
          icon={<User className="h-6 w-6" />} 
          iconClassName="bg-pink-100 text-pink-500"
          onClick={() => handleCardClick("pending-staff")}
          selected={selectedCard === "pending-staff"}
          loading={cardStates["pending-staff"].loading}
          error={cardStates["pending-staff"].data?.error}
          showDataFetched={hasCardData("pending-staff")}
        />
        <StatCard 
          id="pending-assignments"
          title="Clients Pending Assignments" 
          value={getCardValue("pending-assignments")} 
          icon={<FileText className="h-6 w-6" />} 
          iconClassName="bg-teal-100 text-teal-500"
          onClick={() => handleCardClick("pending-assignments")}
          selected={selectedCard === "pending-assignments"}
          loading={cardStates["pending-assignments"].loading}
          error={cardStates["pending-assignments"].data?.error}
          showDataFetched={hasCardData("pending-assignments")}
        />
        
        <StatCard 
          id="provider-missing-info"
          title="Provider Missing Info" 
          value={getCardValue("provider-missing-info")} 
          icon={<FileText className="h-6 w-6" />} 
          iconClassName="bg-red-100 text-red-400"
          onClick={() => handleCardClick("provider-missing-info")}
          selected={selectedCard === "provider-missing-info"}
          loading={cardStates["provider-missing-info"].loading}
          error={cardStates["provider-missing-info"].data?.error}
          showDataFetched={hasCardData("provider-missing-info")}
        />
      </div>

      <div className="mt-8">
        {renderContent()}
      </div>
      <Toaster />
    </div>
  );
};

export default Dashboard;
