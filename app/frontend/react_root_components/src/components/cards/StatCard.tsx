
import { <PERSON>, CardContent, <PERSON>Header, CardTitle } from "@/components/ui/card";
import { cn } from "@/lib/utils";
import { Loader2, AlertCircle } from "lucide-react";

interface StatCardProps {
  title: string;
  value: string | number;
  icon?: React.ReactNode;
  className?: string;
  iconClassName?: string;
  onClick?: () => void;
  selected?: boolean;
  id?: string;
  loading?: boolean;
  error?: string;
  showDataFetched?: boolean;
  animating?: boolean;
}

export function StatCard({
  title,
  value,
  icon,
  className,
  iconClassName,
  onClick,
  selected = false,
  id,
  loading = false,
  error,
  showDataFetched = false,
  animating = false,
}: StatCardProps) {
  return (
    <Card 
      className={cn(
        "overflow-hidden transition-all duration-200 ease-in-out cursor-pointer relative", 
        selected && "ring-2 ring-blue-500 bg-blue-50/50 border-blue-200",
        "hover:shadow-md hover:scale-[1.02]",
        error && "border-red-200 bg-red-50/30",
        className
      )}
      onClick={onClick}
    >
      <CardHeader className="p-4">
        <CardTitle className={cn(
          "text-sm font-medium flex items-center justify-between",
          selected ? "text-blue-700" : "text-muted-foreground",
          error && "text-red-600"
        )}>
          <span>{title}</span>
          {showDataFetched && !loading && !error && (
            <div className="w-2 h-2 bg-green-500 rounded-full" title="Data loaded" />
          )}
        </CardTitle>
      </CardHeader>
      <CardContent className="p-4 pt-0">
        <div className="flex justify-between items-center">
          <div className={cn(
            "text-3xl font-bold flex items-center",
            selected ? "text-blue-800" : "text-foreground",
            error && "text-red-700"
          )}>
            {loading ? (
              <div className="flex items-center gap-2">
                <Loader2 className="h-6 w-6 animate-spin" />
                <span className="text-lg">Loading...</span>
              </div>
            ) : error ? (
              <div className="flex items-center gap-2">
                <AlertCircle className="h-6 w-6 text-red-500" />
                <span className="text-lg">Error</span>
              </div>
            ) : (
              <span className={cn(animating && "animate-count-down")}>
                {value}
              </span>
            )}
          </div>
          {icon && !loading && (
            <div className={cn(
              "rounded-full p-3 transition-colors", 
              iconClassName,
              selected && "ring-2 ring-blue-300",
              error && "opacity-50"
            )}>
              {icon}
            </div>
          )}
        </div>
        {error && (
          <div className="mt-2 text-xs text-red-600 opacity-70">
            {error}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
