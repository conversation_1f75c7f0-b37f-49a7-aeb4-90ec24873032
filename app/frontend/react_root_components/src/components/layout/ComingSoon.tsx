"use client"

import React from "react"
import { motion } from "framer-motion"
import { cn } from "@/lib/utils"
import { Clock } from "lucide-react"

interface ComingSoonProps {
  className?: string
  title?: string
  subtitle?: string
  icon?: React.ReactNode
}

export function ComingSoon({
  className,
  title = "Coming Soon!",
  subtitle = "This feature is currently under development",
  icon = <Clock className="h-8 w-8" />,
}: ComingSoonProps) {
  return (
    <motion.div
      className={cn(
        "w-full mx-auto p-8 rounded-xl border shadow-lg",
        "bg-gradient-to-br from-purple-100 via-blue-50 to-indigo-100",
        "border-purple-200/50",
        className
      )}
      initial={{ opacity: 0, y: -20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.8, ease: "easeOut" }}
    >
      <div className="flex flex-col items-center justify-center text-center min-h-[300px]">
        <motion.div
          className="flex items-center justify-center size-20 bg-gradient-to-br from-purple-500 to-indigo-600 rounded-full mb-6 shadow-lg text-white"
          animate={{ 
            y: [0, -10, 0],
            scale: [1, 1.05, 1]
          }}
          transition={{ 
            duration: 2, 
            repeat: Infinity,
            ease: "easeInOut" 
          }}
        >
          {icon}
        </motion.div>
        
        <h2 className="text-3xl font-bold text-gray-800 mb-3">{title}</h2>
        <p className="text-gray-600 text-lg mb-8 max-w-md">{subtitle}</p>
        
        <div className="flex space-x-2">
          {[...Array(3)].map((_, i) => (
            <motion.div
              key={i}
              className="w-2 h-2 bg-purple-400 rounded-full"
              animate={{ opacity: [0.3, 1, 0.3] }}
              transition={{
                duration: 1.5,
                repeat: Infinity,
                delay: i * 0.2,
              }}
            />
          ))}
        </div>
      </div>
    </motion.div>
  )
}