import { useState, useEffect } from 'react';
import { Search, User, X } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { But<PERSON> } from '@/components/ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { ScrollArea } from '@/components/ui/scroll-area';

interface Client {
  id: string;
  name: string;
  dob: string;
  status?: string;
  therapist?: string;
}

interface ClientSearchProps {
  isOpen: boolean;
  onClose: () => void;
  onSelectClient: (client: Client) => void;
}

const ClientSearch = ({ isOpen, onClose, onSelectClient }: ClientSearchProps) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [searchResults, setSearchResults] = useState<Client[]>([]);
  const [isSearching, setIsSearching] = useState(false);

  // Mock search function - replace with actual API call
  const searchClients = async (term: string) => {
    if (!term.trim()) {
      setSearchResults([]);
      return;
    }

    setIsSearching(true);
    
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500));
    
    // Mock data - replace with actual API call
    const mockClients: Client[] = [
      { id: '001', name: 'Laufer, Channel', dob: '8/30/2016', status: 'Active', therapist: 'Sarah <PERSON>' },
      { id: '002', name: 'Smith, John', dob: '5/15/2018', status: 'Active', therapist: 'Mike Williams' },
      { id: '003', name: 'Johnson, Emma', dob: '12/3/2017', status: 'Active', therapist: 'Sarah Johnson' },
      { id: '004', name: 'Davis, Michael', dob: '7/20/2019', status: 'Inactive', therapist: 'Linda Brown' },
      { id: '005', name: 'Wilson, Sophia', dob: '3/10/2016', status: 'Active', therapist: 'Mike Williams' },
    ];

    // Filter based on search term
    const filtered = mockClients.filter(client =>
      client.name.toLowerCase().includes(term.toLowerCase()) ||
      client.id.includes(term)
    );

    setSearchResults(filtered);
    setIsSearching(false);
  };

  // Debounced search
  useEffect(() => {
    const timer = setTimeout(() => {
      searchClients(searchTerm);
    }, 300);

    return () => clearTimeout(timer);
  }, [searchTerm]);

  const handleSelectClient = (client: Client) => {
    onSelectClient(client);
    setSearchTerm('');
    setSearchResults([]);
  };

  return (
    <Dialog open={isOpen} onOpenChange={() => onClose()}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>Search for Client</DialogTitle>
        </DialogHeader>
        
        <div className="space-y-4">
          {/* Search Input */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input
              type="text"
              placeholder="Search by client name or ID..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
              autoFocus
            />
            {searchTerm && (
              <Button
                variant="ghost"
                size="sm"
                className="absolute right-1 top-1/2 transform -translate-y-1/2"
                onClick={() => setSearchTerm('')}
              >
                <X className="w-4 h-4" />
              </Button>
            )}
          </div>

          {/* Search Results */}
          {isSearching ? (
            <div className="text-center py-8 text-gray-500">
              Searching...
            </div>
          ) : searchResults.length > 0 ? (
            <ScrollArea className="h-[400px] pr-4">
              <div className="space-y-2">
                {searchResults.map((client) => (
                  <div
                    key={client.id}
                    className="p-4 border rounded-lg hover:bg-gray-50 cursor-pointer transition-colors"
                    onClick={() => handleSelectClient(client)}
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex items-start space-x-3">
                        <div className="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center flex-shrink-0">
                          <User className="w-5 h-5 text-purple-600" />
                        </div>
                        <div>
                          <h3 className="font-semibold text-gray-900">{client.name}</h3>
                          <p className="text-sm text-gray-600">ID: {client.id}</p>
                          <p className="text-sm text-gray-600">DOB: {client.dob}</p>
                          {client.therapist && (
                            <p className="text-sm text-gray-600">Therapist: {client.therapist}</p>
                          )}
                        </div>
                      </div>
                      {client.status && (
                        <span className={`text-xs px-2 py-1 rounded-full ${
                          client.status === 'Active' 
                            ? 'bg-green-100 text-green-800' 
                            : 'bg-gray-100 text-gray-800'
                        }`}>
                          {client.status}
                        </span>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </ScrollArea>
          ) : searchTerm ? (
            <div className="text-center py-8 text-gray-500">
              No clients found matching "{searchTerm}"
            </div>
          ) : (
            <div className="text-center py-8 text-gray-500">
              Start typing to search for clients
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default ClientSearch;