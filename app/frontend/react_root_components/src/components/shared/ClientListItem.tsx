
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { User } from "lucide-react";

interface ClientListItemProps {
  name: string;
  onClick?: () => void;
  className?: string;
}

export function ClientListItem({ name, onClick, className }: ClientListItemProps) {
  return (
    <div className={cn("flex items-center justify-between rounded-lg border p-4 mb-4", className)}>
      <div className="flex items-center gap-3">
        <div className="flex h-9 w-9 items-center justify-center rounded-full bg-primary/10 text-primary">
          <User className="h-5 w-5" />
        </div>
        <div>
          <div className="font-medium">{name}</div>
        </div>
      </div>
      <Button size="sm" variant="outline" onClick={onClick} className="rounded-full">
        <svg className="h-5 w-5" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M8 3V5.4C8 5.96005 8 6.24008 7.89101 6.45399C7.79513 6.64215 7.64215 6.79513 7.45399 6.89101C7.24008 7 6.96005 7 6.4 7H5.6C5.03995 7 4.75992 7 4.54601 6.89101C4.35785 6.79513 4.20487 6.64215 4.10899 6.45399C4 6.24008 4 5.96005 4 5.4V3M8 3H16M8 3H7.2C6.08 3 5.52 3 5.092 3.218C4.71565 3.41095 4.41095 3.71565 4.218 4.092C4 4.52 4 5.08 4 6.2V18.8C4 19.92 4 20.48 4.218 20.908C4.41095 21.2844 4.71565 21.589 5.092 21.782C5.52 22 6.08 22 7.2 22H16.8C17.92 22 18.48 22 18.908 21.782C19.2844 21.589 19.589 21.2844 19.782 20.908C20 20.48 20 19.92 20 18.8V6.2C20 5.08 20 4.52 19.782 4.092C19.589 3.71565 19.2844 3.41095 18.908 3.218C18.48 3 17.92 3 16.8 3H16M16 3V5.4C16 5.96005 16 6.24008 16.109 6.45399C16.2049 6.64215 16.3578 6.79513 16.546 6.89101C16.7599 7 17.0399 7 17.6 7H18.4C18.9601 7 19.2401 7 19.454 6.89101C19.6422 6.79513 19.7951 6.64215 19.891 6.45399C20 6.24008 20 5.96005 20 5.4V3M12 16L12 11M9.5 13.5L12 16L14.5 13.5" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
        </svg>
      </Button>
    </div>
  );
}
