import React, { useEffect, useState, useCallback } from 'react'
import { Card, CardContent, CardHeader } from '@/components/ui/card'
import { Phone, AlertTriangle } from 'lucide-react'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { CommunicationTableRow } from './CommunicationTableRow'
import { CommunicationFilters } from './CommunicationFilters'
import { useCommunicationStore } from '@/stores/communicationStore'
import { useToast } from '@/hooks/use-toast'

interface Student {
  id: number
  name: string
  guardianName: string
  guardianPhone: string
  lastContactDate: string | null
  status: 'Never Called' | 'Pending' | 'Overdue' | 'Called'
  daysOverdue: number | null
}

interface ParentCommunicationTableProps {
  userId?: number
  isCaseManager?: boolean
  students?: Student[]
  loading?: boolean
  error?: string
  showCard?: boolean
}

export const ParentCommunicationTable: React.FC<ParentCommunicationTableProps> = ({ 
  userId, 
  isCaseManager, 
  students = [], 
  loading: propLoading = false, 
  error: propError = null, 
  showCard = true 
}) => {
  const { toast } = useToast()
  const [animatingIds, setAnimatingIds] = useState<Set<number>>(new Set())
  
  const {
    setStudents,
    markAsCompleted,
    loading,
    error,
    setLoading,
    setError,
    getFilteredItems,
    getPriorityCounts
  } = useCommunicationStore()

  // Update store when props change
  useEffect(() => {
    setStudents(students)
    setLoading(propLoading)
    setError(propError)
  }, [students, propLoading, propError, setStudents, setLoading, setError])

  const filteredData = getFilteredItems()
  const priorityCounts = getPriorityCounts()

  const handleMarkCompleted = useCallback(async (clientId: number, clientName: string) => {
    try {
      // Add to animating set
      setAnimatingIds(prev => new Set([...prev, clientId]))
      
      // Show optimistic feedback
      toast({
        title: "Marking as complete...",
        description: `Updating communication for ${clientName}`,
      })
      
      // Make API call to mark as complete
      const response = await fetch('/api/dashboard/parent-communications/complete', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ 
          studentId: clientId,
          userId: userId 
        }),
      })

      if (!response.ok) {
        throw new Error('Failed to mark communication as complete')
      }

      // Update store
      setTimeout(() => {
        markAsCompleted(clientId)
        setAnimatingIds(prev => {
          const newSet = new Set(prev)
          newSet.delete(clientId)
          return newSet
        })
        
        toast({
          title: "Success!",
          description: `Communication for ${clientName} marked as complete`,
          className: "bg-emerald-50 border-emerald-200",
        })
      }, 500) // Wait for animation
      
    } catch (error) {
      console.error('Error marking communication as complete:', error)
      
      // Remove from animating
      setAnimatingIds(prev => {
        const newSet = new Set(prev)
        newSet.delete(clientId)
        return newSet
      })
      
      toast({
        title: "Error",
        description: "Failed to mark communication as complete. Please try again.",
        variant: "destructive",
      })
    }
  }, [markAsCompleted, toast, userId])

  const handleMakeCall = useCallback((clientId: number, clientName: string) => {
    // Navigate to phone log page for this client
    window.location.href = `/phonelog?clientId=${clientId}`
  }, [])

  // Get current month for display
  const currentMonth = new Date().toLocaleDateString('en-US', { month: 'long', year: 'numeric' })

  const tableContent = (
    <>
      <div className="flex items-center justify-between mb-6">
        <div>
          {showCard && (
            <>
              <h3 className="text-lg font-semibold flex items-center gap-2">
                <Phone className="h-5 w-5" />
                Parent Communication Manager
                <span className="text-sm font-normal text-muted-foreground">
                  • {currentMonth}
                </span>
              </h3>
              <p className="text-sm text-muted-foreground">
                {filteredData.length} communications requiring attention
                {priorityCounts.overdue > 0 && (
                  <span className="ml-2 text-red-600 font-medium">
                    • {priorityCounts.overdue} overdue
                  </span>
                )}
              </p>
            </>
          )}
          {!showCard && (
            <div>
              <p className="text-sm text-muted-foreground mb-1">
                {currentMonth}
              </p>
              <p className="text-sm text-muted-foreground">
                {filteredData.length} communications requiring attention
                {priorityCounts.overdue > 0 && (
                  <span className="ml-2 text-red-600 font-medium">
                    • {priorityCounts.overdue} overdue
                  </span>
                )}
              </p>
            </div>
          )}
        </div>
        <CommunicationFilters />
      </div>

      {loading ? (
        <div className="text-center py-8">
          <div className="inline-flex items-center gap-2 text-muted-foreground">
            <div className="h-5 w-5 animate-spin rounded-full border-2 border-current border-t-transparent" />
            Loading communications...
          </div>
        </div>
      ) : error ? (
        <div className="text-center py-8 text-red-600">
          <AlertTriangle className="h-6 w-6 mx-auto mb-2" />
          <p>Failed to load communications</p>
          <p className="text-sm text-muted-foreground mt-1">{error}</p>
        </div>
      ) : (
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Client</TableHead>
              <TableHead>Parent/Guardian</TableHead>
              <TableHead>Last Contact</TableHead>
              <TableHead>Priority</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Notes</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredData.length === 0 ? (
              <TableRow>
                <TableCell 
                  colSpan={7}
                  className="text-center py-8 text-muted-foreground"
                >
                  No communications found matching your criteria
                </TableCell>
              </TableRow>
            ) : (
              filteredData.map((comm) => (
                <CommunicationTableRow
                  key={comm.id}
                  {...comm}
                  isAnimating={animatingIds.has(comm.id)}
                  onComplete={handleMarkCompleted}
                  onCall={handleMakeCall}
                />
              ))
            )}
          </TableBody>
        </Table>
      )}
    </>
  )

  return showCard ? (
    <Card>
      <CardHeader>
        <div></div> {/* Empty div for spacing */}
      </CardHeader>
      <CardContent>
        {tableContent}
      </CardContent>
    </Card>
  ) : (
    <div className="space-y-4">
      {tableContent}
    </div>
  )
}