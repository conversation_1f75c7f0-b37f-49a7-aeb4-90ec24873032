import React from 'react'
import { cn } from '@/lib/utils'

interface PriorityIndicatorProps {
  daysOverdue: number
  status: string
}

export const PriorityIndicator: React.FC<PriorityIndicatorProps> = ({ daysOverdue, status }) => {
  // Priority is implicit based on days overdue
  const getPriority = (): { label: string; className: string; dotColor: string } => {
    if (status === 'Never Called' || daysOverdue > 30) {
      return {
        label: 'High',
        className: 'bg-red-500/10 text-red-600 ring-red-500/30',
        dotColor: 'bg-red-500'
      }
    } else if (daysOverdue > 14) {
      return {
        label: 'Medium',
        className: 'bg-orange-500/10 text-orange-600 ring-orange-500/30',
        dotColor: 'bg-orange-500'
      }
    }
    return {
      label: 'Low',
      className: 'bg-gray-500/10 text-gray-600 ring-gray-500/30',
      dotColor: 'bg-gray-500'
    }
  }

  const priority = getPriority()

  return (
    <div className={cn(
      'inline-flex items-center gap-1.5 rounded-full px-2.5 py-0.5 text-xs font-medium ring-1 ring-inset',
      priority.className
    )}>
      <span className={cn('mr-1 h-2 w-2 rounded-full', priority.dotColor)} />
      {priority.label}
    </div>
  )
}