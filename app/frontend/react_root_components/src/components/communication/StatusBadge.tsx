import React from 'react'
import { Clock, AlertTriangle, CheckCircle2, X } from 'lucide-react'
import { cn } from '@/lib/utils'

interface StatusBadgeProps {
  status: 'Pending' | 'Overdue' | 'Completed' | 'Never Called' | 'Called'
  daysOverdue?: number | null
}

export const StatusBadge: React.FC<StatusBadgeProps> = ({ status, daysOverdue }) => {
  const isOverdue = status === 'Overdue' || (daysOverdue !== null && daysOverdue !== undefined && daysOverdue > 30)
  const actualStatus = isOverdue ? 'Overdue' : status === 'Called' ? 'Completed' : status

  const variants = {
    Pending: 'bg-blue-500/10 text-blue-600 ring-blue-500/30',
    Overdue: 'bg-red-500/10 text-red-600 ring-red-500/30',
    Completed: 'bg-emerald-500/10 text-emerald-600 ring-emerald-500/30',
    'Never Called': 'bg-gray-500/10 text-gray-600 ring-gray-500/30',
  }

  const icons = {
    Pending: <Clock className="h-3 w-3" />,
    Overdue: <AlertTriangle className="h-3 w-3" />,
    Completed: <CheckCircle2 className="h-3 w-3" />,
    'Never Called': <X className="h-3 w-3" />,
  }

  return (
    <div className={cn(
      'inline-flex items-center gap-1.5 rounded-full px-2.5 py-0.5 text-xs font-medium ring-1 ring-inset',
      variants[actualStatus]
    )}>
      {icons[actualStatus]}
      {actualStatus}
    </div>
  )
}