import React from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
  DropdownMenuLabel,
} from '@/components/ui/dropdown-menu'
import { Search, X, Filter } from 'lucide-react'
import { useCommunicationStore } from '@/stores/communicationStore'

export const CommunicationFilters: React.FC = () => {
  const {
    searchQuery,
    statusFilter,
    setSearchQuery,
    setStatusFilter,
    getPriorityCounts,
    getCommunicationItems
  } = useCommunicationStore()

  const priorityCounts = getPriorityCounts()
  const allItems = getCommunicationItems()
  
  // Calculate priority counts based on implicit priority
  const priorityBreakdown = allItems.reduce((acc, item) => {
    // Skip completed items
    if (item.status === 'Completed') return acc
    
    // Calculate priority based on days overdue
    let priority: 'high' | 'medium' | 'low' = 'low'
    if (item.status === 'Never Called' || item.daysOverdue > 30) {
      priority = 'high'
    } else if (item.daysOverdue > 14) {
      priority = 'medium'
    }
    
    acc[priority] = (acc[priority] || 0) + 1
    return acc
  }, {} as Record<string, number>)

  return (
    <div className="flex items-center gap-2">
      <div className="relative">
        <Input
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          placeholder="Search clients or parents..."
          className="pl-9 pr-9 w-64"
        />
        <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center justify-center pl-3 text-muted-foreground/80">
          <Search className="h-4 w-4" />
        </div>
        {searchQuery && (
          <button
            className="absolute inset-y-0 right-0 flex h-full w-9 items-center justify-center rounded-r-lg text-muted-foreground/80 outline-offset-2 transition-colors hover:text-foreground focus:z-10 focus-visible:outline focus-visible:outline-2 focus-visible:outline-ring/70 disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50"
            aria-label="Clear search"
            onClick={() => setSearchQuery("")}
          >
            <X className="h-4 w-4" />
          </button>
        )}
      </div>
      
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="outline" size="sm" className="gap-2">
            <Filter className="h-4 w-4 text-muted-foreground" />
            <span>Filter</span>
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-48">
          <DropdownMenuLabel>Priority</DropdownMenuLabel>
          <DropdownMenuCheckboxItem disabled className="text-muted-foreground text-xs">
            Based on days overdue
          </DropdownMenuCheckboxItem>
          <div className="px-2 py-1 text-xs space-y-1">
            <div className="flex justify-between">
              <span>High (&gt;30 days)</span>
              <span>{priorityBreakdown.high || 0}</span>
            </div>
            <div className="flex justify-between">
              <span>Medium (15-30)</span>
              <span>{priorityBreakdown.medium || 0}</span>
            </div>
            <div className="flex justify-between">
              <span>Low (&lt;15 days)</span>
              <span>{priorityBreakdown.low || 0}</span>
            </div>
          </div>
          <DropdownMenuSeparator />
          <DropdownMenuLabel>Status</DropdownMenuLabel>
          <DropdownMenuCheckboxItem
            checked={statusFilter === 'all'}
            onCheckedChange={() => setStatusFilter('all')}
          >
            All Status
          </DropdownMenuCheckboxItem>
          <DropdownMenuCheckboxItem
            checked={statusFilter === 'Pending'}
            onCheckedChange={() => setStatusFilter('Pending')}
          >
            Pending
          </DropdownMenuCheckboxItem>
          <DropdownMenuCheckboxItem
            checked={statusFilter === 'Overdue'}
            onCheckedChange={() => setStatusFilter('Overdue')}
          >
            Overdue ({priorityCounts.overdue})
          </DropdownMenuCheckboxItem>
          <DropdownMenuCheckboxItem
            checked={statusFilter === 'Never Called'}
            onCheckedChange={() => setStatusFilter('Never Called')}
          >
            Never Called ({priorityCounts.neverCalled})
          </DropdownMenuCheckboxItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  )
}