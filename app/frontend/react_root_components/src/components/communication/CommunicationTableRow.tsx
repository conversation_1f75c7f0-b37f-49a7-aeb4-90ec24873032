import React from 'react'
import { TableCell, TableRow } from '@/components/ui/table'
import { cn } from '@/lib/utils'
import { CommunicationActions } from './CommunicationActions'
import { StatusBadge } from './StatusBadge'
import { PriorityIndicator } from './PriorityIndicator'

interface CommunicationTableRowProps {
  id: number
  clientName: string
  parentName: string
  lastContact: string | null
  daysOverdue: number
  status: 'Pending' | 'Overdue' | 'Completed' | 'Never Called'
  notes: string
  isAnimating?: boolean
  onComplete: (id: number, clientName: string) => void
  onCall: (id: number, clientName: string) => void
}

export const CommunicationTableRow: React.FC<CommunicationTableRowProps> = ({
  id,
  clientName,
  parentName,
  lastContact,
  daysOverdue,
  status,
  notes,
  isAnimating = false,
  onComplete,
  onCall
}) => {
  const getRowClassName = () => {
    if (status === 'Overdue' || daysOverdue > 7) {
      return 'bg-red-50 hover:bg-red-100'
    }
    if (daysOverdue > 3) {
      return 'bg-yellow-50 hover:bg-yellow-100'
    }
    return ''
  }

  return (
    <TableRow 
      className={cn(
        getRowClassName(),
        isAnimating && 'animate-fade-out-right transition-all duration-500'
      )}
    >
      <TableCell className="font-medium">{clientName}</TableCell>
      <TableCell>{parentName}</TableCell>
      <TableCell>
        <div className="flex flex-col">
          <span>
            {lastContact 
              ? new Date(lastContact).toLocaleDateString() 
              : 'Never contacted'}
          </span>
          {daysOverdue > 0 && (
            <span className="text-xs text-red-600">
              {daysOverdue} days ago
            </span>
          )}
        </div>
      </TableCell>
      <TableCell>
        <PriorityIndicator daysOverdue={daysOverdue} status={status} />
      </TableCell>
      <TableCell>
        <StatusBadge status={status} daysOverdue={daysOverdue} />
      </TableCell>
      <TableCell className="max-w-xs truncate">
        {notes}
      </TableCell>
      <TableCell className="text-right">
        <CommunicationActions
          id={id}
          clientName={clientName}
          status={status}
          onComplete={onComplete}
          onCall={onCall}
          disabled={isAnimating}
        />
      </TableCell>
    </TableRow>
  )
}