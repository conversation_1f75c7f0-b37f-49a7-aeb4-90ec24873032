import React from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Phone, Check } from 'lucide-react'
import { cn } from '@/lib/utils'

interface CommunicationActionsProps {
  id: number
  clientName: string
  status: string
  onComplete: (id: number, clientName: string) => void
  onCall: (id: number, clientName: string) => void
  disabled?: boolean
}

export const CommunicationActions: React.FC<CommunicationActionsProps> = ({
  id,
  clientName,
  status,
  onComplete,
  onCall,
  disabled = false
}) => {
  return (
    <div className="flex items-center gap-2 justify-end">
      <Button
        size="sm"
        variant="secondary"
        onClick={() => onCall(id, clientName)}
        title="Log Phone Call"
        disabled={disabled}
        className="h-7 px-2.5 rounded-md text-xs font-medium shadow-sm shadow-black/5"
      >
        <Phone className="mr-1.5 h-3.5 w-3.5 opacity-80" aria-hidden="true" />
        Call
      </Button>
      
      {status !== 'Completed' && (
        <Button
          size="sm"
          variant="default"
          onClick={() => onComplete(id, clientName)}
          title="Complete Phone Call"
          disabled={disabled}
          className={cn(
            'h-7 px-2.5 rounded-md text-xs font-medium shadow-sm shadow-black/5',
            'bg-emerald-500 hover:bg-emerald-600 disabled:opacity-50'
          )}
        >
          <Check className="mr-1.5 h-3.5 w-3.5 opacity-80" aria-hidden="true" />
          Complete
        </Button>
      )}
    </div>
  )
}