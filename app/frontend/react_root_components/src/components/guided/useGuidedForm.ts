import { useState, useEffect, useCallback } from 'react'
import { 
  UseGuidedFormOptions, 
  UseGuidedFormReturn, 
  GuidePrompt, 
  GuidanceResponse, 
  ProcessedResponse,
  FormContext
} from './types'

export function useGuidedForm({
  promptId,
  context,
  autoGeneratePrompts = true
}: UseGuidedFormOptions): UseGuidedFormReturn {
  const [prompt, setPrompt] = useState<GuidePrompt | null>(null)
  const [guidance, setGuidance] = useState<GuidanceResponse | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Fetch the guide prompt configuration
  const fetchPrompt = useCallback(async () => {
    if (!promptId) return

    setIsLoading(true)
    setError(null)
    
    try {
      // TODO: Replace with actual API call
      const response = await fetch(`/api/guided-form/prompts/${promptId}`)
      if (!response.ok) {
        throw new Error(`Failed to fetch prompt: ${response.statusText}`)
      }
      
      const promptData: GuidePrompt = await response.json()
      setPrompt(promptData)
      
      // Auto-generate guidance if enabled
      if (autoGeneratePrompts) {
        await generateGuidance(promptData, context)
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch prompt'
      setError(errorMessage)
      console.error('Error fetching guide prompt:', err)
    } finally {
      setIsLoading(false)
    }
  }, [promptId, context, autoGeneratePrompts])

  // Generate AI guidance based on prompt and context
  const generateGuidance = useCallback(async (
    guidePrompt?: GuidePrompt, 
    formContext?: FormContext
  ) => {
    const activePrompt = guidePrompt || prompt
    const activeContext = formContext || context
    
    if (!activePrompt) {
      setError('No prompt available for guidance generation')
      return
    }

    setIsLoading(true)
    setError(null)

    try {
      // TODO: Replace with actual API call
      const response = await fetch('/api/guided-form/guidance', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          promptId: activePrompt.id,
          context: activeContext
        })
      })

      if (!response.ok) {
        throw new Error(`Failed to generate guidance: ${response.statusText}`)
      }

      const guidanceData: GuidanceResponse = await response.json()
      setGuidance(guidanceData)
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to generate guidance'
      setError(errorMessage)
      setGuidance({
        prompts: [],
        contextSummary: '',
        suggestions: [],
        error: errorMessage
      })
      console.error('Error generating guidance:', err)
    } finally {
      setIsLoading(false)
    }
  }, [prompt, context])

  // Process user input through LLM
  const processInput = useCallback(async (input: string): Promise<ProcessedResponse> => {
    if (!prompt) {
      throw new Error('No prompt available for input processing')
    }

    try {
      // TODO: Replace with actual API call
      const response = await fetch('/api/guided-form/process', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          promptId: prompt.id,
          context,
          input
        })
      })

      if (!response.ok) {
        throw new Error(`Failed to process input: ${response.statusText}`)
      }

      const processedData: ProcessedResponse = await response.json()
      return processedData
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to process input'
      console.error('Error processing input:', err)
      throw new Error(errorMessage)
    }
  }, [prompt, context])

  // Refresh the current prompt data
  const refreshPrompt = useCallback(async () => {
    await fetchPrompt()
  }, [fetchPrompt])

  // Initial fetch when promptId changes
  useEffect(() => {
    if (promptId) {
      fetchPrompt()
    } else {
      setPrompt(null)
      setGuidance(null)
      setError(null)
    }
  }, [promptId, fetchPrompt])

  return {
    prompt,
    guidance,
    isLoading,
    error,
    generateGuidance: () => generateGuidance(),
    processInput,
    refreshPrompt
  }
}