import React, { useState, useEffect, useRef } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Textarea } from '@/components/ui/textarea'
import { Badge } from '@/components/ui/badge'
import { Loader2, Sparkles, MessageCircle, FileText, Send, RefreshCw } from 'lucide-react'
import { cn } from '@/lib/utils'
import { 
  GuidedFormProps, 
  FormData, 
  GuidanceResponse, 
  ProcessedResponse 
} from './types'

export default function GuidedForm({
  guidePrompt,
  context,
  onSubmit,
  mode = 'hybrid',
  className,
  placeholder,
  disabled = false
}: GuidedFormProps) {
  const [input, setInput] = useState('')
  const [guidance, setGuidance] = useState<GuidanceResponse | null>(null)
  const [isGeneratingGuidance, setIsGeneratingGuidance] = useState(false)
  const [isProcessing, setIsProcessing] = useState(false)
  const [showSuggestions, setShowSuggestions] = useState(false)
  const textareaRef = useRef<HTMLTextAreaElement>(null)

  // Auto-generate guidance when component mounts
  useEffect(() => {
    if (guidePrompt && !guidance && !isGeneratingGuidance) {
      generateGuidance()
    }
  }, [guidePrompt])

  const generateGuidance = async () => {
    if (!guidePrompt || isGeneratingGuidance) return

    setIsGeneratingGuidance(true)
    try {
      // TODO: Replace with actual API call
      const mockGuidance: GuidanceResponse = {
        prompts: [
          "How did the conversation go with the parent/guardian?",
          "What concerns or questions did they raise?",
          "Any changes in the child's behavior or progress to discuss?"
        ],
        contextSummary: `Preparing guidance for ${guidePrompt.name}`,
        suggestions: [
          "Start with a friendly greeting and introduction",
          "Ask about any concerns or changes at home",
          "Discuss recent progress and goals"
        ]
      }
      
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000))
      setGuidance(mockGuidance)
    } catch (error) {
      setGuidance({
        prompts: [],
        contextSummary: '',
        suggestions: [],
        error: 'Failed to generate guidance'
      })
    } finally {
      setIsGeneratingGuidance(false)
    }
  }

  const processInput = async (inputText: string): Promise<ProcessedResponse> => {
    // TODO: Replace with actual API call
    const mockResponse: ProcessedResponse = {
      rawInput: inputText,
      structuredData: {
        sentiment: 'positive',
        keyPoints: ['parent engagement', 'progress discussed'],
        actionItems: ['follow up next week']
      },
      summary: `Phone call completed with positive parent engagement. Key topics covered: ${inputText.substring(0, 50)}...`,
      actionItems: ['Schedule follow-up call', 'Update student file']
    }
    
    // Simulate processing delay
    await new Promise(resolve => setTimeout(resolve, 800))
    return mockResponse
  }

  const handleSubmit = async () => {
    if (!input.trim() || isProcessing) return

    setIsProcessing(true)
    try {
      const processed = await processInput(input)
      
      const formData: FormData = {
        rawInput: input,
        structuredData: processed.structuredData,
        summary: processed.summary,
        actionItems: processed.actionItems,
        metadata: {
          promptId: guidePrompt.id,
          context,
          timestamp: new Date().toISOString()
        }
      }

      onSubmit(formData)
      setInput('') // Clear input after successful submission
    } catch (error) {
      console.error('Failed to process input:', error)
      // TODO: Show error toast
    } finally {
      setIsProcessing(false)
    }
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && (e.metaKey || e.ctrlKey)) {
      e.preventDefault()
      handleSubmit()
    }
  }

  const insertSuggestion = (suggestion: string) => {
    const currentInput = input
    const newInput = currentInput ? `${currentInput}\n\n${suggestion}` : suggestion
    setInput(newInput)
    setShowSuggestions(false)
    textareaRef.current?.focus()
  }

  const getModeIcon = () => {
    switch (mode) {
      case 'conversation': return <MessageCircle className="h-4 w-4" />
      case 'structured': return <FileText className="h-4 w-4" />
      default: return <Sparkles className="h-4 w-4" />
    }
  }

  const getModeLabel = () => {
    switch (mode) {
      case 'conversation': return 'Conversational'
      case 'structured': return 'Structured'
      default: return 'AI-Guided'
    }
  }

  return (
    <div className={cn('space-y-4', className)}>
      {/* Header with mode indicator */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Badge variant="secondary" className="gap-1.5">
            {getModeIcon()}
            {getModeLabel()}
          </Badge>
          <span className="text-sm text-muted-foreground">
            {guidePrompt.purpose}
          </span>
        </div>
        <Button
          variant="outline"
          size="sm"
          onClick={generateGuidance}
          disabled={isGeneratingGuidance}
          className="gap-2"
        >
          {isGeneratingGuidance ? (
            <Loader2 className="h-3 w-3 animate-spin" />
          ) : (
            <RefreshCw className="h-3 w-3" />
          )}
          Refresh Guidance
        </Button>
      </div>

      {/* Guidance Panel */}
      {guidance && !guidance.error && (
        <Card className="border-dashed">
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <Sparkles className="h-4 w-4 text-blue-500" />
              AI Guidance
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            {guidance.contextSummary && (
              <p className="text-xs text-muted-foreground">
                {guidance.contextSummary}
              </p>
            )}
            
            {guidance.prompts.length > 0 && (
              <div>
                <p className="text-xs font-medium mb-2">Suggested Questions:</p>
                <div className="space-y-1">
                  {guidance.prompts.map((prompt, index) => (
                    <button
                      key={index}
                      className="block w-full text-left text-xs p-2 rounded-md bg-muted/50 hover:bg-muted transition-colors"
                      onClick={() => insertSuggestion(prompt)}
                    >
                      {prompt}
                    </button>
                  ))}
                </div>
              </div>
            )}

            {guidance.suggestions.length > 0 && (
              <div>
                <p className="text-xs font-medium mb-2">Tips:</p>
                <ul className="space-y-1">
                  {guidance.suggestions.map((suggestion, index) => (
                    <li key={index} className="text-xs text-muted-foreground flex items-start gap-1">
                      <span className="w-1 h-1 rounded-full bg-muted-foreground mt-1.5 flex-shrink-0" />
                      {suggestion}
                    </li>
                  ))}
                </ul>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Error State */}
      {guidance?.error && (
        <Card className="border-destructive/50 bg-destructive/5">
          <CardContent className="pt-4">
            <p className="text-sm text-destructive">{guidance.error}</p>
          </CardContent>
        </Card>
      )}

      {/* Main Input Area */}
      <div className="space-y-3">
        <Textarea
          ref={textareaRef}
          value={input}
          onChange={(e) => setInput(e.target.value)}
          onKeyDown={handleKeyDown}
          placeholder={placeholder || "Describe your interaction... (Cmd/Ctrl + Enter to submit)"}
          disabled={disabled || isProcessing}
          className="min-h-[120px] resize-y"
        />
        
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            {input.length > 0 && (
              <span className="text-xs text-muted-foreground">
                {input.length} characters
              </span>
            )}
          </div>
          
          <Button
            onClick={handleSubmit}
            disabled={!input.trim() || isProcessing}
            className="gap-2"
          >
            {isProcessing ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <Send className="h-4 w-4" />
            )}
            {isProcessing ? 'Processing...' : 'Submit'}
          </Button>
        </div>
      </div>

      {/* Loading State for Guidance */}
      {isGeneratingGuidance && !guidance && (
        <Card className="border-dashed">
          <CardContent className="pt-4">
            <div className="flex items-center gap-2 text-muted-foreground">
              <Loader2 className="h-4 w-4 animate-spin" />
              <span className="text-sm">Generating AI guidance...</span>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}