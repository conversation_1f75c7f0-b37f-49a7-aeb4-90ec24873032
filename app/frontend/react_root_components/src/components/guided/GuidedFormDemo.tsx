import React from 'react'
import { GuidedForm } from './index'
import type { GuidePrompt, FormContext, FormData } from './types'

// Demo component to test GuidedForm functionality
export default function GuidedFormDemo() {
  const demoPrompt: GuidePrompt = {
    id: 'phone-log-demo',
    name: 'Phone Log Demo',
    purpose: 'Document monthly parent communication calls',
    contextFields: ['studentName', 'parentName', 'lastCallDate'],
    systemPrompt: 'You are assisting a case manager in documenting a phone call with a parent/guardian.',
    userPrompts: [
      {
        template: 'How did the conversation go with {parentName}?',
        timing: 'initial',
        required: true
      },
      {
        template: 'What concerns or questions did {parentName} raise about {studentName}?',
        timing: 'followup'
      },
      {
        template: 'Are there any action items or follow-ups needed?',
        timing: 'closing'
      }
    ],
    outputSchema: {
      conversationSummary: 'string',
      parentConcerns: 'string[]',
      actionItems: 'string[]',
      followUpDate: 'string'
    }
  }

  const demoContext: FormContext = {
    studentName: '<PERSON>',
    parentName: '<PERSON>',
    lastCallDate: '2024-05-15',
    studentId: 123,
    caseManagerId: 456
  }

  const handleSubmit = (data: FormData) => {
    console.log('Form submitted with data:', data)
    alert('Form submitted successfully! Check console for details.')
  }

  return (
    <div className="max-w-2xl mx-auto p-6 space-y-6">
      <div>
        <h1 className="text-2xl font-bold mb-2">GuidedForm Demo</h1>
        <p className="text-muted-foreground">
          This demo shows the GuidedForm component in action for phone log documentation.
        </p>
      </div>

      <GuidedForm
        guidePrompt={demoPrompt}
        context={demoContext}
        onSubmit={handleSubmit}
        mode="hybrid"
        placeholder="Describe your phone call with the parent/guardian..."
        className="border rounded-lg p-4"
      />

      <div className="text-sm text-muted-foreground bg-muted/50 p-4 rounded-lg">
        <h3 className="font-medium mb-2">Demo Context:</h3>
        <pre className="whitespace-pre-wrap">
          {JSON.stringify(demoContext, null, 2)}
        </pre>
      </div>
    </div>
  )
}