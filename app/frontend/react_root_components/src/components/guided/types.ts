export interface PromptTemplate {
  template: string;
  timing: 'initial' | 'followup' | 'closing';
  required?: boolean;
}

export interface OutputSchema {
  [key: string]: string | object;
}

export interface GuidePrompt {
  id: string;
  name: string;
  purpose: string;
  contextFields: string[];
  systemPrompt: string;
  userPrompts: PromptTemplate[];
  outputSchema: OutputSchema;
}

export interface FormContext {
  [key: string]: any;
}

export interface GuidanceResponse {
  prompts: string[];
  contextSummary: string;
  suggestions: string[];
  loading?: boolean;
  error?: string;
}

export interface ProcessedResponse {
  rawInput: string;
  structuredData: object;
  summary: string;
  actionItems: string[];
}

export interface GuidedFormProps {
  guidePrompt: GuidePrompt;
  context: FormContext;
  onSubmit: (data: FormData) => void;
  mode?: 'conversation' | 'structured' | 'hybrid';
  className?: string;
  placeholder?: string;
  disabled?: boolean;
}

export interface FormData {
  rawInput: string;
  structuredData?: object;
  summary?: string;
  actionItems?: string[];
  metadata?: {
    promptId: string;
    context: FormContext;
    timestamp: string;
  };
}

export interface UseGuidedFormOptions {
  promptId: string;
  context: FormContext;
  autoGeneratePrompts?: boolean;
}

export interface UseGuidedFormReturn {
  prompt: GuidePrompt | null;
  guidance: GuidanceResponse | null;
  isLoading: boolean;
  error: string | null;
  generateGuidance: () => Promise<void>;
  processInput: (input: string) => Promise<ProcessedResponse>;
  refreshPrompt: () => Promise<void>;
}