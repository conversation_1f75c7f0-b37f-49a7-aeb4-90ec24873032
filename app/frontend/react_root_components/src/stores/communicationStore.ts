import { create } from 'zustand'
import { devtools, persist } from 'zustand/middleware'

interface Student {
  id: number
  name: string
  guardianName: string
  guardianPhone: string
  lastContactDate: string | null
  status: 'Never Called' | 'Pending' | 'Overdue' | 'Called'
  daysOverdue: number | null
}

interface CommunicationItem {
  id: number
  clientName: string
  parentName: string
  lastContact: string | null
  daysOverdue: number
  status: 'Pending' | 'Overdue' | 'Completed' | 'Never Called'
  phone: string
  notes: string
}

interface CommunicationState {
  // Data
  students: Student[]
  completedIds: Set<number>
  
  // UI State
  searchQuery: string
  statusFilter: 'all' | 'Pending' | 'Overdue' | 'Completed' | 'Never Called'
  loading: boolean
  error: string | null
  
  // Actions
  setStudents: (students: Student[]) => void
  markAsCompleted: (id: number) => void
  setSearchQuery: (query: string) => void
  setStatusFilter: (filter: CommunicationState['statusFilter']) => void
  setLoading: (loading: boolean) => void
  setError: (error: string | null) => void
  resetFilters: () => void
  
  // Computed values
  getCommunicationItems: () => CommunicationItem[]
  getFilteredItems: () => CommunicationItem[]
  getPriorityCounts: () => { overdue: number; pending: number; neverCalled: number }
}

// Helper function to calculate priority based on days overdue
const getPriorityFromDaysOverdue = (daysOverdue: number | null, status: string): 'high' | 'medium' | 'low' => {
  if (status === 'Never Called' || (daysOverdue && daysOverdue > 30)) {
    return 'high'
  } else if (daysOverdue && daysOverdue > 14) {
    return 'medium'
  }
  return 'low'
}

export const useCommunicationStore = create<CommunicationState>()(
  devtools(
    persist(
      (set, get) => ({
        // Initial state
        students: [],
        completedIds: new Set(),
        searchQuery: '',
        statusFilter: 'all',
        loading: false,
        error: null,
        
        // Actions
        setStudents: (students) => set({ students }),
        
        markAsCompleted: (id) => set((state) => ({
          completedIds: new Set([...state.completedIds, id])
        })),
        
        setSearchQuery: (searchQuery) => set({ searchQuery }),
        
        setStatusFilter: (statusFilter) => set({ statusFilter }),
        
        setLoading: (loading) => set({ loading }),
        
        setError: (error) => set({ error }),
        
        resetFilters: () => set({ 
          searchQuery: '', 
          statusFilter: 'all' 
        }),
        
        // Computed values
        getCommunicationItems: () => {
          const { students } = get()
          
          return students.map(student => {
            // Map status
            let status: CommunicationItem['status'] = 'Pending'
            if (student.status === 'Never Called') {
              status = 'Never Called'
            } else if (student.status === 'Overdue' || (student.daysOverdue && student.daysOverdue > 30)) {
              status = 'Overdue'
            } else if (student.status === 'Called') {
              status = 'Completed'
            }
            
            return {
              id: student.id,
              clientName: student.name,
              parentName: student.guardianName,
              lastContact: student.lastContactDate,
              daysOverdue: student.daysOverdue || 0,
              status,
              phone: student.guardianPhone,
              notes: student.status === 'Never Called' ? 'Initial contact needed' : 'Monthly check-in'
            }
          })
        },
        
        getFilteredItems: () => {
          const { searchQuery, statusFilter, completedIds } = get()
          const items = get().getCommunicationItems()
          
          return items.filter(item => {
            // Filter out completed items
            if (completedIds.has(item.id)) {
              return false
            }
            
            // Search filter
            const matchesSearch = searchQuery === '' || 
              item.clientName.toLowerCase().includes(searchQuery.toLowerCase()) ||
              item.parentName.toLowerCase().includes(searchQuery.toLowerCase())
            
            // Status filter
            const matchesStatus = statusFilter === 'all' || item.status === statusFilter
            
            return matchesSearch && matchesStatus
          })
        },
        
        getPriorityCounts: () => {
          const items = get().getCommunicationItems()
          const { completedIds } = get()
          
          const activeItems = items.filter(item => !completedIds.has(item.id))
          
          return {
            overdue: activeItems.filter(item => 
              item.status === 'Overdue' || item.daysOverdue > 7
            ).length,
            pending: activeItems.filter(item => 
              item.status === 'Pending'
            ).length,
            neverCalled: activeItems.filter(item => 
              item.status === 'Never Called'
            ).length
          }
        }
      }),
      {
        name: 'communication-store',
        partialize: (state) => ({ 
          completedIds: Array.from(state.completedIds) // Convert Set to Array for persistence
        }),
        merge: (persistedState: any, currentState) => ({
          ...currentState,
          ...persistedState,
          completedIds: new Set(persistedState?.completedIds || []) // Convert Array back to Set
        })
      }
    )
  )
)