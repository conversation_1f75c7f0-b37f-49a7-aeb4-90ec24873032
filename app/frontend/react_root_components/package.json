{"name": "trinote-dashboard-components", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "build:dev": "VITE_STAGEWISE=true vite build", "preview": "vite preview", "test": "vitest", "test:run": "vitest run"}, "dependencies": {"@radix-ui/react-accordion": "^1.1.2", "@radix-ui/react-alert-dialog": "^1.0.5", "@radix-ui/react-aspect-ratio": "^1.0.3", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-collapsible": "^1.0.3", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-hover-card": "^1.0.7", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-menubar": "^1.0.4", "@radix-ui/react-navigation-menu": "^1.1.4", "@radix-ui/react-popover": "^1.0.7", "@radix-ui/react-progress": "^1.0.3", "@radix-ui/react-radio-group": "^1.1.3", "@radix-ui/react-scroll-area": "^1.0.5", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slider": "^1.1.2", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-toggle": "^1.0.3", "@radix-ui/react-toggle-group": "^1.0.4", "@radix-ui/react-tooltip": "^1.0.7", "@stagewise/toolbar-react": "^0.4.3", "@tanstack/react-query": "^5.56.2", "@tanstack/react-table": "^8.21.3", "class-variance-authority": "^0.7.0", "classnames": "^2.5.1", "clsx": "^2.0.0", "cmdk": "^0.2.0", "date-fns": "^2.30.0", "embla-carousel-react": "^8.0.0", "framer-motion": "^12.14.0", "input-otp": "^1.2.4", "lucide-react": "^0.356.0", "next-themes": "^0.2.1", "react": "^18.2.0", "react-day-picker": "^8.10.0", "react-dom": "^18.2.0", "react-hook-form": "^7.48.2", "react-resizable-panels": "^0.0.55", "react-router-dom": "^6.26.1", "recharts": "^2.9.0", "sonner": "^1.4.0", "tailwind-merge": "^2.2.0", "tailwindcss-animate": "^1.0.7", "vaul": "^0.9.0", "zustand": "^5.0.5"}, "devDependencies": {"@stagewise-plugins/react": "^0.4.8", "@stagewise/toolbar": "0.4.8", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.5.2", "jsdom": "^24.0.0", "@types/node": "^20.10.5", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "tailwindcss": "^3.4.0", "typescript": "^5.2.2", "vite": "^5.0.10", "vitest": "^3.2.4"}}