# Amazon Q Project Profile for Trinote 2.0

This directory contains configuration files and context hooks for Amazon Q to provide better assistance with the Trinote 2.0 project.

## Contents

- `project-profile.json`: Main configuration file that describes the project structure, frameworks, and conventions
- `context-hooks/`: Scripts that provide additional context to Amazon Q

## Context Hooks

- `database-schema.py`: Provides database schema information
- `project-structure.py`: Generates a map of the project directory structure
- `code-patterns.py`: Defines common code patterns used in the project

## Usage

Amazon Q will automatically use this profile when you run commands in this project directory. No additional configuration is needed.

## Updating the Profile

As the project evolves, you may need to update the profile:

1. Update `project-profile.json` when adding new frameworks or changing conventions
2. Modify context hooks to reflect changes in project structure or patterns
3. Add new context hooks for additional project-specific information

## Benefits

- More accurate code suggestions
- Better understanding of project structure
- Consistent code generation following project conventions
- Improved assistance with database queries and models
