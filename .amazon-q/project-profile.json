{"name": "Trinote 2.0", "description": "A modern task management system for ABA providers built with Flask", "version": "1.0.0", "projectStructure": {"rootDirectory": "/home/<USER>/code/trinote2.0", "sourceDirectories": ["app", "scripts", "tasks", "migrations"], "testDirectories": ["tests"], "configurationFiles": ["config.py", ".env.example", "docker-compose.yml", "Dockerfile"], "ignorePaths": [".git", ".venv", "__pycache__", "node_modules"]}, "frameworks": [{"name": "Flask", "version": "^2.0.0"}, {"name": "SQLAlchemy", "version": "^1.4.0"}], "languages": [{"name": "Python", "version": "^3.8"}, {"name": "JavaScript", "version": "ES6"}, {"name": "HTML/CSS", "version": "5"}], "databases": [{"name": "PostgreSQL", "version": "^17.0", "role": "primary"}, {"name": "SQL Server", "version": "^2022", "role": "secondary"}], "architecture": {"type": "MVC", "components": [{"name": "Models", "path": "app/models.py", "description": "SQLAlchemy database models"}, {"name": "Views", "path": "app/templates", "description": "Jinja2 templates"}, {"name": "Controllers", "path": "app/routes.py", "description": "Flask route handlers"}, {"name": "Blueprints", "path": "app/blueprints", "description": "Modular Flask blueprints"}]}, "codePatterns": {"routeDefinition": {"pattern": "@routes.route('/endpoint', methods=['GET', 'POST'])\n@login_required\ndef endpoint_name():\n    # Permission check\n    if not current_user.has_permission('required_permission'):\n        flash('Permission denied', 'danger')\n        return redirect(url_for('routes.index'))\n    \n    # Logic here\n    \n    return render_template('template.html', context=context_data)", "description": "Standard route definition with permission check"}, "modelDefinition": {"pattern": "class ModelName(db.Model):\n    __tablename__ = 'model_names'\n    \n    id = db.<PERSON>umn(db.<PERSON><PERSON><PERSON>, primary_key=True)\n    name = db.Column(db.String(100), nullable=False)\n    created_at = db.Column(db.DateTime, default=datetime.utcnow)\n    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)\n    \n    # Relationships\n    related_models = db.relationship('RelatedModel', backref='model_name', lazy=True)\n    \n    def __repr__(self):\n        return f'<ModelName {self.name}>'\n    \n    def to_dict(self):\n        return {\n            'id': self.id,\n            'name': self.name,\n            'created_at': self.created_at,\n            'updated_at': self.updated_at\n        }", "description": "Standard SQLAlchemy model definition"}, "permissionCheck": {"pattern": "def check_permission(permission_name):\n    user_id = session.get('user_id')\n    if not user_id:\n        return False\n    \n    user = User.query.get(user_id)\n    if not user:\n        return False\n    \n    return user.has_permission(permission_name)", "description": "Standard permission check function"}}, "conventions": {"naming": {"variables": "snake_case", "functions": "snake_case", "classes": "PascalCase", "constants": "UPPER_SNAKE_CASE", "files": "snake_case.py", "tables": "snake_case_plural"}, "formatting": {"indentation": "4 spaces", "lineLength": 100, "quoteStyle": "single quotes for strings, double quotes for docstrings"}, "documentation": {"docstringStyle": "Google", "requireDocstrings": ["classes", "public_methods", "modules"]}}, "workflows": {"development": {"setup": "docker-compose up -d", "testing": "pytest", "linting": "flake8 app tests", "formatting": "black app tests"}, "database": {"migrations": {"create": "flask db migrate -m \"Description of changes\"", "apply": "flask db upgrade", "rollback": "flask db downgrade"}}, "deployment": {"environments": ["dev", "stage", "prod"], "tool": "<PERSON><PERSON><PERSON>", "commands": {"deploy": "pulumi up --stack {environment}"}}}, "security": {"authentication": "Custom middleware (app/middleware/auth.py)", "authorization": "Role-based with user-specific overrides", "secretsManagement": "HashiCorp Vault"}, "customRules": [{"name": "RequirePermissionCheck", "description": "Routes that modify data should check permissions", "pattern": "if not current_user.has_permission('required_permission')"}, {"name": "UseToDict", "description": "Models should implement to_dict method for serialization", "pattern": "def to_dict(self):"}, {"name": "IncludeTimestamps", "description": "Models should include created_at and updated_at timestamps", "pattern": "updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)"}]}