"""
Amazon Q Context Hook for Project Structure

This script provides project structure information to Amazon Q
to improve assistance with project navigation and understanding.
"""

import os
import json
from pathlib import Path

def get_directory_structure(startpath, max_depth=3, exclude_dirs=None):
    """
    Generate a nested dictionary that represents the folder structure of startpath
    """
    if exclude_dirs is None:
        exclude_dirs = ['.git', '.venv', '__pycache__', 'node_modules', '.pytest_cache']
    
    structure = {}
    
    for root, dirs, files in os.walk(startpath):
        # Skip excluded directories
        dirs[:] = [d for d in dirs if d not in exclude_dirs]
        
        # Calculate current depth
        level = root.replace(startpath, '').count(os.sep)
        if level > max_depth:
            continue
        
        # Get relative path
        rel_path = os.path.relpath(root, startpath)
        if rel_path == '.':
            rel_path = ''
        
        # Add files to structure
        if files:
            # Create nested dict based on path
            parts = rel_path.split(os.sep) if rel_path else []
            current = structure
            for part in parts:
                if part not in current:
                    current[part] = {}
                current = current[part]
            
            # Add files
            current['__files__'] = sorted(files)
    
    return structure

# Get project root
project_root = Path(__file__).parent.parent.parent

# Generate structure
structure = get_directory_structure(project_root)

# Print structure as JSON
print(json.dumps(structure, indent=2))
