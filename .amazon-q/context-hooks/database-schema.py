"""
Amazon Q Context Hook for Database Schema

This script provides database schema information to Amazon Q
to improve assistance with database-related queries.
"""

import os
import sys
import json
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

try:
    # Try to import SQLAlchemy models
    from app.models import db, User, Role, Permission, Message, RolePermission, UserPermission, RoutePermission
    
    # Define schema information
    schema_info = {
        "models": []
    }
    
    # Helper function to get column info
    def get_column_info(model):
        columns = []
        for column_name in model.__table__.columns.keys():
            column = model.__table__.columns[column_name]
            column_info = {
                "name": column_name,
                "type": str(column.type),
                "nullable": column.nullable,
                "primary_key": column.primary_key,
                "foreign_key": None
            }
            
            # Check for foreign keys
            for fk in column.foreign_keys:
                column_info["foreign_key"] = {
                    "table": fk.column.table.name,
                    "column": fk.column.name
                }
            
            columns.append(column_info)
        return columns
    
    # Get model information
    for model in [User, Role, Permission, Message, RolePermission, UserPermission, RoutePermission]:
        model_info = {
            "name": model.__name__,
            "table_name": model.__tablename__,
            "columns": get_column_info(model)
        }
        schema_info["models"].append(model_info)
    
    # Print schema information as JSON
    print(json.dumps(schema_info, indent=2))
    
except Exception as e:
    # If there's an error, return a simplified schema based on model inspection
    print(json.dumps({
        "error": str(e),
        "models": [
            {
                "name": "User",
                "table_name": "users",
                "description": "User accounts with role-based permissions"
            },
            {
                "name": "Role",
                "table_name": "roles",
                "description": "User roles with associated permissions"
            },
            {
                "name": "Permission",
                "table_name": "permissions",
                "description": "System permissions that can be assigned to roles or users"
            },
            {
                "name": "Message",
                "table_name": "messages",
                "description": "Internal messaging system"
            },
            {
                "name": "RolePermission",
                "table_name": "role_permissions",
                "description": "Many-to-many relationship between roles and permissions"
            },
            {
                "name": "UserPermission",
                "table_name": "user_permissions",
                "description": "Direct permission assignments to users"
            },
            {
                "name": "RoutePermission",
                "table_name": "route_permissions",
                "description": "Associates permissions with application routes"
            }
        ]
    }, indent=2))
