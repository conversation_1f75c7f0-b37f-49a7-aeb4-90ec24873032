"""
Amazon Q Context Hook for Code Patterns

This script provides common code patterns to Amazon Q
to improve code generation and recommendations.
"""

import json

# Define common code patterns used in the project
patterns = {
    "route_definition": {
        "pattern": """
@routes.route('/endpoint', methods=['GET', 'POST'])
@login_required
def endpoint_name():
    """Endpoint description."""
    # Permission check
    if not current_user.has_permission('required_permission'):
        flash('Permission denied', 'danger')
        return redirect(url_for('routes.index'))
    
    # Logic here
    
    return render_template('template.html', context=context_data)
""",
        "description": "Standard route definition with permission check"
    },
    
    "model_definition": {
        "pattern": """
class ModelName(db.Model):
    """Model description."""
    __tablename__ = 'model_names'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    related_models = db.relationship('RelatedModel', backref='model_name', lazy=True)
    
    def __repr__(self):
        return f'<ModelName {self.name}>'
    
    def to_dict(self):
        """Convert model to dictionary."""
        return {
            'id': self.id,
            'name': self.name,
            'created_at': self.created_at,
            'updated_at': self.updated_at
        }
""",
        "description": "Standard SQLAlchemy model definition"
    },
    
    "blueprint_registration": {
        "pattern": """
def register_blueprint(app):
    from .views import blueprint_name
    app.register_blueprint(blueprint_name, url_prefix='/prefix')
""",
        "description": "Blueprint registration pattern"
    },
    
    "permission_check": {
        "pattern": """
def check_permission(permission_name):
    """Check if current user has the specified permission."""
    user_id = session.get('user_id')
    if not user_id:
        return False
    
    user = User.query.get(user_id)
    if not user:
        return False
    
    return user.has_permission(permission_name)
""",
        "description": "Standard permission check function"
    },
    
    "database_connection": {
        "pattern": """
def get_db_connection(read_only=False):
    """Get database connection based on configuration."""
    if app.config.get('USE_POSTGRES_ONLY'):
        # Use SQLAlchemy session for PostgreSQL
        return db.session
    else:
        # For SQL Server operations
        from app.database import get_read_only_connection, get_write_connection
        return get_read_only_connection() if read_only else get_write_connection()
""",
        "description": "Database connection pattern"
    },
    
    "error_handling": {
        "pattern": """
@app.errorhandler(404)
def page_not_found(e):
    """Handle 404 errors."""
    return render_template('errors/404.html'), 404

@app.errorhandler(500)
def server_error(e):
    """Handle 500 errors."""
    # Log the error
    app.logger.error(f"500 error: {str(e)}")
    return render_template('errors/500.html'), 500
""",
        "description": "Error handling pattern"
    },
    
    "test_pattern": {
        "pattern": """
def test_feature_name(client, auth):
    """Test description."""
    # Setup
    auth.login()
    
    # Action
    response = client.get('/endpoint')
    
    # Assertions
    assert response.status_code == 200
    assert b'Expected content' in response.data
""",
        "description": "Testing pattern"
    }
}

# Print patterns as JSON
print(json.dumps(patterns, indent=2))
