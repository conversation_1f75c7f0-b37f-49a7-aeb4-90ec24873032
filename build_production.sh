#!/usr/bin/env bash

# Trinote 2.0 Production Build Script
# Simple manual build script for production deployment
# No task runners, just straightforward commands

set -e  # Exit on any error

echo "🏗️  Trinote 2.0 Production Build"
echo "================================="
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_step() {
    echo -e "${BLUE}📋 $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check if we're in the right directory
if [ ! -f "requirements.txt" ] || [ ! -f "requirements-test.txt" ]; then
    print_error "This script must be run from the project root directory"
    print_error "Make sure you're in the directory containing requirements.txt"
    exit 1
fi

# Step 1: Check for required tools
print_step "Checking for required tools..."

# Check for uv
if ! command -v uv &> /dev/null; then
    print_warning "uv not found. Installing uv..."
    if command -v pip &> /dev/null; then
        pip install uv
        print_success "uv installed successfully"
    else
        print_error "pip not found. Please install Python and pip first"
        exit 1
    fi
else
    print_success "uv found"
fi

# Check for pnpm
if ! command -v pnpm &> /dev/null; then
    print_warning "pnpm not found. Installing pnpm..."
    if command -v npm &> /dev/null; then
        npm install -g pnpm
        print_success "pnpm installed successfully"
    elif command -v curl &> /dev/null; then
        curl -fsSL https://get.pnpm.io/install.sh | sh
        # Source the shell profile to get pnpm in PATH
        export PATH="$HOME/.local/share/pnpm:$PATH"
        print_success "pnpm installed successfully"
    else
        print_error "Neither npm nor curl found. Please install Node.js/npm or curl first"
        exit 1
    fi
else
    print_success "pnpm found"
fi

# Step 2: Setup Python virtual environment
print_step "Setting up Python virtual environment..."

VENV_PATH=".venv"

if [ ! -d "$VENV_PATH" ]; then
    print_step "Creating virtual environment..."
    uv venv "$VENV_PATH"
    print_success "Virtual environment created"
else
    print_success "Virtual environment already exists"
fi

# Step 3: Activate virtual environment
print_step "Activating virtual environment..."
source "$VENV_PATH/bin/activate"
print_success "Virtual environment activated"

# Step 4: Install Python dependencies
print_step "Installing Python dependencies..."

print_step "Installing main requirements..."
uv pip install -r requirements.txt
print_success "Main requirements installed"

print_step "Installing test requirements..."
uv pip install -r requirements-test.txt
print_success "Test requirements installed"

# Step 5: Build React frontend
print_step "Building React frontend..."

# Check if react_root_components directory exists
if [ ! -d "app/frontend/react_root_components" ]; then
    print_error "React components directory not found at app/frontend/react_root_components"
    exit 1
fi

cd app/frontend/react_root_components

# Install frontend dependencies
print_step "Installing frontend dependencies..."
pnpm install
print_success "Frontend dependencies installed"

# Build for production
print_step "Building React components for production..."
pnpm run build
print_success "React frontend built successfully"

# Return to project root
cd ../../..

# Step 6: Final checks and summary
print_step "Running final checks..."

# Check if build output exists
if [ -d "app/static/js/react-root-components" ]; then
    print_success "React build output found in app/static/js/react-root-components"
else
    print_warning "React build output directory not found - check build configuration"
fi

# Check if virtual environment is still active
if [[ "$VIRTUAL_ENV" != "" ]]; then
    print_success "Python virtual environment is active"
else
    print_warning "Python virtual environment is not active"
fi

echo ""
echo "🎉 Production Build Complete!"
echo "=============================="
echo ""
echo "📁 Build Artifacts:"
echo "   • Python virtual environment: .venv/"
echo "   • React build output: app/static/js/react-root-components/"
echo ""
echo "🚀 Next Steps:"
echo "   • Your Python virtual environment is activated for this session"
echo "   • React components are built and ready for production"
echo "   • You can now run your Flask application with: python run.py"
echo ""
echo "💡 To reactivate the virtual environment later:"
echo "   source .venv/bin/activate"
echo ""
print_success "Build script completed successfully!"
