# Virtual Environment
venv/
env/
.env
.env.*

# Python
__pycache__/
*.py[cod]
*$py.class

# IDE
.vscode/
.idea/

# Misc
.DS_Store
.env*

# AI Configs
gptme.toml
.clinerules
.windsurfrules

# Vault
.vault-sync.conf
.aider*

# Pulumi
Pulumi.*.yaml

# Python/Virtualenv
__pycache__/
*.py[cod]
*$py.class
.venv/
venv/
env/
.python-version

# Build/Deployment artifacts
*.so
*.pyd
*.o
*.obj
*.egg-info/
dist/
build/

.specstory
.coverage
.mcp.json
mcp.json

# Direnv
.direnv/

**/.claude/settings.local.json

# Added by Claude Task Master
# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
dev-debug.log
# Dependency directories
node_modules/
# Environment variables
# Editor directories and files
.idea
.vscode
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
# OS specific

*.bak
*logs
# SpecStory explanation file
.specstory/.what-is-this.md

# Deployment script (local use only)
deploy.sh

# Added by Task Master AI
# Task files
tasks.json
.taskmaster/
cookies.txt
mem.json
history.txt
.roo*
.claude-flow-history

# Added by Task Master AI
.taskmaster
