name: Deploy to Production

on:
  push:
    branches:
      - main

jobs:
  deploy:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Set up SSH
        run: |
          mkdir -p ~/.ssh
          echo "${{ secrets.DEPLOY_KEY }}" > ~/.ssh/deploy_key
          chmod 600 ~/.ssh/deploy_key

          # Configure SSH to recognize the server
          echo "Host prod-server
            HostName **************
            User root
            IdentityFile ~/.ssh/deploy_key
            StrictHostKeyChecking=no" >> ~/.ssh/config

      - name: Deploy to Server
        run: ssh prod-server "bash /root/trinote2.0/deploy.sh"
