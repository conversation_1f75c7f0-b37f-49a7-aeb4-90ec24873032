name: Build and Test

on:
  push:
    branches: [ main, develop, 'feature/**' ]
  pull_request:
    branches: [ main, develop ]
  workflow_dispatch:

env:
  PYTHON_VERSION: '3.12'
  NODE_VERSION: '20'

jobs:
  native-build-test:
    name: Native Build and Test
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgres:17
        env:
          POSTGRES_USER: trinote
          POSTGRES_PASSWORD: Admin123!
          POSTGRES_DB: db
        ports:
          - 15432:5432
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
      
      mssql:
        image: mcr.microsoft.com/mssql/server:2022-latest
        env:
          ACCEPT_EULA: Y
          SA_PASSWORD: Admin123!
          MSSQL_PID: Express
        ports:
          - 1433:1433
        options: >-
          --health-cmd "/opt/mssql-tools18/bin/sqlcmd -S localhost -U sa -P Admin123! -C -Q 'SELECT 1'"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Python ${{ env.PYTHON_VERSION }}
      uses: actions/setup-python@v5
      with:
        python-version: ${{ env.PYTHON_VERSION }}

    - name: Set up Node.js ${{ env.NODE_VERSION }}
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}

    - name: Install pnpm
      run: npm install -g pnpm

    - name: Cache Python dependencies
      uses: actions/cache@v4
      with:
        path: ~/.cache/pip
        key: ${{ runner.os }}-pip-${{ hashFiles('**/requirements*.txt') }}
        restore-keys: |
          ${{ runner.os }}-pip-

    - name: Cache pnpm dependencies
      uses: actions/cache@v4
      with:
        path: ~/.pnpm-store
        key: ${{ runner.os }}-pnpm-${{ hashFiles('**/pnpm-lock.yaml') }}
        restore-keys: |
          ${{ runner.os }}-pnpm-

    - name: Install Python dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install -r requirements-test.txt

    - name: Install frontend dependencies
      working-directory: app/frontend/react_root_components
      run: pnpm install

    - name: Build frontend
      working-directory: app/frontend/react_root_components
      run: pnpm run build

    - name: Install MSSQL tools
      run: |
        curl https://packages.microsoft.com/keys/microsoft.asc | sudo tee /etc/apt/trusted.gpg.d/microsoft.asc
        curl https://packages.microsoft.com/config/ubuntu/$(lsb_release -rs)/prod.list | sudo tee /etc/apt/sources.list.d/mssql-release.list
        sudo apt-get update
        sudo ACCEPT_EULA=Y apt-get install -y mssql-tools18 unixodbc-dev

    - name: Set up environment
      run: |
        cp .env.local .env
        # Update database connection strings for GitHub Actions services
        sed -i 's/localhost:15432/localhost:15432/g' .env
        sed -i 's/localhost:1433/localhost:1433/g' .env

    - name: Debug Environment Setup
      run: |
        echo "=== Environment File Contents ==="
        cat .env
        echo ""
        echo "=== Environment Variables ==="
        env | grep -E "(POSTGRES|MSSQL|FLASK)" | sort || true
        echo "=== Current Directory ==="
        pwd
        echo "=== Directory Contents ==="
        ls -la

    - name: Wait for databases to be ready
      run: |
        echo "Waiting for PostgreSQL to be ready..."
        timeout 120 bash -c 'until pg_isready -h localhost -p 15432 -U trinote; do echo "Waiting for PostgreSQL..."; sleep 5; done'
        echo "PostgreSQL is ready!"
        
        echo "Waiting for MSSQL to be ready..."
        timeout 120 bash -c 'until /opt/mssql-tools18/bin/sqlcmd -S localhost -U sa -P Admin123! -C -Q "SELECT 1" > /dev/null 2>&1; do echo "Waiting for MSSQL..."; sleep 5; done'
        echo "MSSQL is ready!"

    - name: Initialize MSSQL database
      run: |
        echo "Creating MSSQL database..."
        /opt/mssql-tools18/bin/sqlcmd -S localhost -U sa -P Admin123! -C -Q "CREATE DATABASE trinote_mssql"
        echo "MSSQL database created successfully!"
        
        echo "Running MSSQL initialization script..."
        python -m scripts.init_mssql
        echo "MSSQL initialization completed!"
      env:
        MSSQL_HOST: localhost
        MSSQL_PORT: 1433
        MSSQL_USER: sa
        MSSQL_PASSWORD: Admin123!
        MSSQL_DATABASE: trinote_mssql

    - name: Run PostgreSQL migrations
      run: |
        echo "Running PostgreSQL migrations..."
        flask db upgrade
        echo "PostgreSQL migrations completed!"
      env:
        FLASK_APP: run.py
        DATABASE_URL: postgresql://trinote:Admin123!@localhost:15432/db

    - name: Run Python tests
      run: |
        echo "Starting Python tests..."
        pytest -v --tb=short -m "not slow" || exit 1
        echo "Python tests completed successfully!"
      env:
        FLASK_APP: run.py
        FLASK_ENV: testing
        SECRET_KEY: test-secret-key-for-ci
        JWT_SECRET_KEY: test-jwt-secret-key-for-ci
        DATABASE_URL: postgresql://trinote:Admin123!@localhost:15432/db
        SQLALCHEMY_DATABASE_URI: postgresql://trinote:Admin123!@localhost:15432/db
        MSSQL_CONNECTION_STRING: mssql+pyodbc://sa:Admin123!@localhost:1433/trinote_mssql?driver=ODBC+Driver+18+for+SQL+Server&TrustServerCertificate=yes
        POSTGRES_HOST: localhost
        POSTGRES_PORT: 15432
        POSTGRES_DB: db
        POSTGRES_USER: trinote
        POSTGRES_PASSWORD: Admin123!
        MSSQL_SERVER: localhost
        MSSQL_USERNAME: sa
        MSSQL_PASSWORD: Admin123!
        MSSQL_NAME: trinote_mssql
        BYPASS_AUTH_FOR_ADMIN: false

    - name: Run frontend tests
      working-directory: app/frontend/react_root_components
      run: |
        echo "Starting frontend tests..."
        pnpm test:run
        echo "Frontend tests completed successfully!"

    - name: Start application and check health
      run: |
        echo "Starting Flask application..."
        # Start the Flask app in the background
        python run.py &
        APP_PID=$!
        
        # Wait for app to start with retries
        echo "Waiting for application to start..."
        for i in {1..30}; do
          if curl -f http://localhost:5000/health > /dev/null 2>&1; then
            echo "Application health check passed!"
            break
          fi
          echo "Attempt $i: Application not ready yet, waiting..."
          sleep 2
        done
        
        # Final health check
        echo "Performing final health check..."
        curl -f http://localhost:5000/health || (echo "Health check failed!" && kill $APP_PID && exit 1)
        
        echo "Application is running successfully!"
        # Clean up
        kill $APP_PID
      env:
        FLASK_APP: run.py
        FLASK_ENV: testing
        SECRET_KEY: test-secret-key-for-ci
        JWT_SECRET_KEY: test-jwt-secret-key-for-ci
        DATABASE_URL: postgresql://trinote:Admin123!@localhost:15432/db
        SQLALCHEMY_DATABASE_URI: postgresql://trinote:Admin123!@localhost:15432/db
        MSSQL_CONNECTION_STRING: mssql+pyodbc://sa:Admin123!@localhost:1433/trinote_mssql?driver=ODBC+Driver+18+for+SQL+Server&TrustServerCertificate=yes
        POSTGRES_HOST: localhost
        POSTGRES_PORT: 15432
        POSTGRES_DB: db
        POSTGRES_USER: trinote
        POSTGRES_PASSWORD: Admin123!
        MSSQL_SERVER: localhost
        MSSQL_USERNAME: sa
        MSSQL_PASSWORD: Admin123!
        MSSQL_NAME: trinote_mssql
        BYPASS_AUTH_FOR_ADMIN: false

    - name: Show logs on failure
      if: failure()
      run: |
        echo "=== CI DEBUGGING INFORMATION ==="
        echo "=== Environment Variables ==="
        env | grep -E "(POSTGRES|MSSQL|FLASK|DATABASE)" | sort || true
        echo ""
        echo "=== Process List ==="
        ps aux | grep -E "(postgres|mssql|flask|python)" || true
        echo ""
        echo "=== Network Connections ==="
        netstat -tlnp | grep -E "(5432|1433|5000)" || true
        echo ""
        echo "=== Disk Usage ==="
        df -h || true
        echo ""
        echo "=== Environment File ==="
        cat .env || true
        echo ""
        echo "=== Application Logs (if any) ==="
        tail -50 *.log 2>/dev/null || echo "No log files found"

  docker-compose-build-test:
    name: Docker Compose Build and Test
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: Set up environment
      run: |
        cp .env.local .env

    - name: Build Docker images
      run: |
        echo "Building Docker images..."
        docker compose build
        echo "Docker images built successfully!"

    - name: Start Docker Compose services
      run: |
        echo "Starting Docker Compose services..."
        docker compose up -d
        
        # Wait for services to be healthy with better error handling
        echo "Waiting for services to be healthy..."
        for i in {1..60}; do
          if docker compose ps | grep -E "healthy.*healthy" > /dev/null 2>&1; then
            echo "All services are healthy!"
            break
          fi
          echo "Attempt $i: Services not ready yet, waiting..."
          sleep 5
        done
        
        # Show service status
        echo "=== Service Status ==="
        docker compose ps

    - name: Run database initialization
      run: |
        echo "Waiting for databases to be fully ready..."
        sleep 30
        
        echo "Initializing PostgreSQL..."
        docker compose exec -T app flask db upgrade
        
        echo "Initializing MSSQL..."
        docker compose exec -T app python -m scripts.init_mssql
        
        echo "Database initialization completed!"

    - name: Run tests in Docker container
      run: |
        echo "Running backend tests in Docker..."
        docker compose exec -T app pytest -v --tb=short -m "not slow"
        
        echo "Running frontend tests in Docker..."
        docker compose exec -T app bash -c "cd app/frontend/react_root_components && pnpm test:run"
        
        echo "All tests completed successfully!"

    - name: Check application health
      run: |
        echo "Checking application health..."
        # Check if the app is responding
        curl -f http://localhost:5001/health || (echo "Health check failed!" && exit 1)
        
        # Check version endpoint
        curl -f http://localhost:5001/version || (echo "Version check failed!" && exit 1)
        
        echo "Application health checks passed!"

    - name: Show logs on failure
      if: failure()
      run: |
        echo "=== DOCKER DEBUGGING INFORMATION ==="
        echo "=== Docker Compose Logs ==="
        docker compose logs
        echo ""
        echo "=== Docker Compose Status ==="
        docker compose ps
        echo ""
        echo "=== Docker Images ==="
        docker images
        echo ""
        echo "=== Docker Networks ==="
        docker network ls

    - name: Stop services
      if: always()
      run: |
        echo "Stopping Docker Compose services..."
        docker compose down -v
        echo "Services stopped successfully!"