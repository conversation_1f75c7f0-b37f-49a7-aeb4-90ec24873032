version: 2.1

orbs:
  python: circleci/python@3.1

commands:
  install_doctl:
    description: "Install DigitalOcean CLI"
    steps:
      - run:
          name: Install DigitalOcean CLI
          command: |
            cd /usr/local/bin
            sudo wget https://github.com/digitalocean/doctl/releases/download/v1.101.0/doctl-1.101.0-linux-amd64.tar.gz
            sudo tar xf doctl-1.101.0-linux-amd64.tar.gz
            sudo chmod +x doctl
            doctl version

executors:
  python-executor:
    docker:
      - image: cimg/python:3.12.3
    environment:
      FLASK_ENV: prod
      PYTHONUNBUFFERED: 1
      ACCEPT_EULA: Y

jobs:
  test:
    executor: python-executor
    steps:
      - checkout

      - python/install-packages:
          pkg-manager: pip
          pip-dependency-file: requirements.txt
          cache-version: v2

      - python/install-packages:
          pkg-manager: pip
          pip-dependency-file: requirements-test.txt
          cache-version: v2

      - run:
          name: Install Test Dependencies
          command: |
            pip install pytest pytest-cov

      - run:
          name: Run Tests
          command: |
            echo "Running tests with coverage..."
            pytest --cov=./ --cov-report=xml

      - store_artifacts:
          path: coverage.xml
          destination: coverage-report

  build-and-test:
    machine:
      image: ubuntu-2004:2023.07.1
    steps:
      - checkout

      # Set up Docker Compose
      - run:
          name: Install Docker Compose
          command: |
            docker-compose version

      # Create test environment file
      - run:
          name: Create test environment configuration
          command: |
            # Create a .env file for the test environment
            echo "# Test Environment Configuration" > .env
            echo "APP_PORT=5001" >> .env
            echo "MSSQL_PORT=1433" >> .env
            echo "" >> .env
            echo "# Use local databases for testing" >> .env
            echo "MSSQL_SERVER=mssql_db" >> .env
            echo "MSSQL_NAME=master" >> .env
            echo "MSSQL_USERNAME=sa" >> .env
            echo "MSSQL_PASSWORD=Admin123!" >> .env

      # Start services with docker-compose
      - run:
          name: Start Docker services
          command: |
            # Use the local profile to start local MSSQL with explicit project name
            docker-compose -p trinote20 --profile local up -d
            
            # Show what containers are running
            echo "=== Docker containers status ==="
            docker ps -a --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
            
            # Wait for services to be healthy
            echo "Waiting for services to be ready..."
            docker-compose -p trinote20 ps
            
            # Wait for PostgreSQL
            echo "Waiting for PostgreSQL to be ready..."
            for i in {1..30}; do
              if docker-compose -p trinote20 exec -T postgres_db pg_isready -U trinote; then
                echo "PostgreSQL is ready!"
                break
              fi
              echo "PostgreSQL not ready yet, attempt $i/30..."
              sleep 2
            done
            
            # Wait for MSSQL
            echo "Waiting for MSSQL to be ready..."
            for i in {1..60}; do
              if docker-compose -p trinote20 exec -T mssql_db /opt/mssql-tools18/bin/sqlcmd -S localhost -U sa -P Admin123! -C -Q "SELECT 1" >/dev/null 2>&1; then
                echo "MSSQL is ready!"
                break
              fi
              echo "MSSQL not ready yet, attempt $i/60..."
              sleep 2
            done

      # Initialize databases
      - run:
          name: Initialize databases
          command: |
            # Run database initialization
            docker-compose -p trinote20 run --rm db-init
            echo "Databases initialized"

      # Run tests inside the app container
      - run:
          name: Run tests with coverage
          command: |
            # Install test dependencies in the app container
            docker-compose -p trinote20 exec -T app pip install pytest pytest-cov
            
            # Create test results directory
            docker-compose -p trinote20 exec -T app mkdir -p test-results
            
            # Run tests with proper database connections
            docker-compose -p trinote20 exec -T app pytest tests/ -v --cov=app --cov-report=html --cov-report=xml --junitxml=test-results/junit.xml
            
            # Copy test results out of container
            docker cp $(docker-compose -p trinote20 ps -q app):/app/test-results ./test-results
            docker cp $(docker-compose -p trinote20 ps -q app):/app/htmlcov ./htmlcov
            docker cp $(docker-compose -p trinote20 ps -q app):/app/coverage.xml ./coverage.xml || true

      # Save test results
      - store_test_results:
          path: test-results

      # Save coverage report
      - store_artifacts:
          path: htmlcov
          destination: coverage-report
          
      - store_artifacts:
          path: coverage.xml
          destination: coverage.xml

      # Clean up
      - run:
          name: Clean up Docker services
          command: |
            docker-compose -p trinote20 down -v
          when: always

  # Development deployment job removed because we only care about production

  build-and-push-image:
    machine:
      image: ubuntu-2004:2023.07.1
    steps:
      - checkout
      - install_doctl

      - run:
          name: Authenticate with DigitalOcean Container Registry
          command: |
            source $BASH_ENV
            doctl auth init -t $DIGITALOCEAN_TOKEN
            doctl registry login

      - run:
          name: Skip Validation - Going Straight to Production!
          command: |
            source $BASH_ENV
            echo "🤠 COWBOY DEPLOYMENT! No time for validation!"

      - run:
          name: Build and Tag Docker Image
          command: |
            source $BASH_ENV
            # Build using the git short hash as a tag
            IMAGE_TAG=$(git rev-parse --short HEAD)
            # Also build as 'latest'
            docker build -t registry.digitalocean.com/trinote-registry-prod/trinote-app:${IMAGE_TAG} -t registry.digitalocean.com/trinote-registry-prod/trinote-app:latest .

      - run:
          name: Push Docker Images to Registry
          command: |
            source $BASH_ENV
            # Push both the tagged version and latest
            IMAGE_TAG=$(git rev-parse --short HEAD)
            docker push registry.digitalocean.com/trinote-registry-prod/trinote-app:${IMAGE_TAG}
            docker push registry.digitalocean.com/trinote-registry-prod/trinote-app:latest

      - run:
          name: Deploy to DigitalOcean App Platform with Enhanced Observability
          command: |
            source $BASH_ENV
            echo "Starting DigitalOcean App Platform deployment with enhanced observability..."
            ./scripts/deploy-to-do.sh

            # Check if deployment logs exist and save as artifact
            if [ -f "deployment_logs.txt" ]; then
              echo "Deployment logs found - saving as artifact..."
              mkdir -p deployment_artifacts
              cp deployment_logs.txt deployment_artifacts/
              
              # Also extract and save error messages if they exist
              if [ -f "deployment_errors.txt" ]; then
                cp deployment_errors.txt deployment_artifacts/
              fi
            fi

            # Check deployment status file
            if [ -f "deployment_errors.txt" ]; then
              echo "⚠️ Deployment errors found! Check logs for details."
              cat deployment_errors.txt
            fi

      - run:
          name: Verify Deployment Status and Run Health Checks
          command: |
            source $BASH_ENV
            echo "Performing post-deployment health checks..."
            APP_ID=$(doctl apps list --format ID --no-header | head -1)
            APP_URL=$(doctl apps get "$APP_ID" --format DefaultIngress --no-header)

            # Create a health check report file
            HEALTH_CHECK_REPORT="health_check_report.txt"
            echo "=== DEPLOYMENT HEALTH CHECK REPORT: $(date) ===" > $HEALTH_CHECK_REPORT
            echo "App URL: $APP_URL" >> $HEALTH_CHECK_REPORT

            # Run multiple health checks
            echo "Testing application health endpoints..."
            HTTP_STATUS=$(curl -s -o /dev/null -w "%{http_code}" "$APP_URL/health")
            echo "Health endpoint status: $HTTP_STATUS"
            echo "Health endpoint status: $HTTP_STATUS" >> $HEALTH_CHECK_REPORT

            # Get health endpoint content for more insight
            echo "Health endpoint content:" >> $HEALTH_CHECK_REPORT
            curl -s "$APP_URL/health" >> $HEALTH_CHECK_REPORT
            echo "" >> $HEALTH_CHECK_REPORT

            if [[ "$HTTP_STATUS" == "200" ]]; then
              echo "✅ Application health check passed"
              echo "✅ Application health check passed" >> $HEALTH_CHECK_REPORT
            else
              echo "❌ Application health check failed with status $HTTP_STATUS"
              echo "❌ Application health check failed with status $HTTP_STATUS" >> $HEALTH_CHECK_REPORT
              echo "Deployment may still be in progress or application may have issues"
              echo "Deployment may still be in progress or application may have issues" >> $HEALTH_CHECK_REPORT
              
              # Collect additional diagnostic information
              echo "==== ADDITIONAL DIAGNOSTICS ====" >> $HEALTH_CHECK_REPORT
              echo "Checking deployment status..." >> $HEALTH_CHECK_REPORT
              doctl apps list-deployments "$APP_ID" --format ID,Cause,Progress,Phase,Created,Updated >> $HEALTH_CHECK_REPORT
              
              echo "Checking application logs..." >> $HEALTH_CHECK_REPORT
              doctl apps logs "$APP_ID" -t run | tail -n 50 >> $HEALTH_CHECK_REPORT
            fi

            # Display app information
            echo "Application deployment details:"
            APP_INFO=$(doctl apps get "$APP_ID" --format Spec.Name,DefaultIngress,ActiveDeployment.Phase,Updated)
            echo "$APP_INFO"
            echo "Application deployment details:" >> $HEALTH_CHECK_REPORT
            echo "$APP_INFO" >> $HEALTH_CHECK_REPORT

            # Save report to artifacts directory
            mkdir -p deployment_artifacts
            cp $HEALTH_CHECK_REPORT deployment_artifacts/

            # Display summary
            echo "Health check report saved to $HEALTH_CHECK_REPORT"

      # Store deployment artifacts
      - store_artifacts:
          path: deployment_artifacts
          destination: deployment

  deploy-with-pulumi:
    executor: python-executor
    steps:
      - checkout

      # Install Python dependencies needed for Pulumi
      - python/install-packages:
          pkg-manager: pip
          pip-dependency-file: requirements.txt
          cache-version: v2

      # Install DigitalOcean CLI
      - install_doctl
      # Install Pulumi
      - run:
          name: Install Pulumi
          command: |
            curl -fsSL https://get.pulumi.com | sh
            echo 'export PATH="$HOME/.pulumi/bin:$PATH"' >> $BASH_ENV

      - run:
          name: Configure Pulumi for Deployment
          command: |
            source $BASH_ENV

            # Configure Pulumi
            pulumi login
            cd pulumi

            # Select the organization's production stack
            echo "Selecting the triumph organization's prod stack"
            pulumi stack select triumph/prod

            # Define environment based on branch
            ENVIRONMENT="prod" # default for main branch
            if [ "$CIRCLE_BRANCH" != "main" ]; then
              ENVIRONMENT="dev" # use 'dev' environment for non-main branches
            fi

            # Set core configuration
            echo "Setting Pulumi configuration for environment: $ENVIRONMENT"
            pulumi config set environment $ENVIRONMENT
            pulumi config set branch $CIRCLE_BRANCH
      - run:
          name: Deploy with Pulumi
          command: |
            source $BASH_ENV 
            # Update the infrastructure
            echo "🚀 DEPLOYING INFRASTRUCTURE..."
            pulumi refresh --yes # First refresh to ensure state matches reality
            pulumi up --yes
            
            # Get the version (commit SHA) that was deployed
            APP_VERSION=$(pulumi stack output version)
            echo "✅ Deployed version: $APP_VERSION"
            
            # Save version to an artifact for reference
            echo $APP_VERSION > ../deployment-version.txt

      - run:
          name: Verify Deployment
          command: |
            source $BASH_ENV
            # Get application URL from Pulumi outputs
            APP_URL=$(pulumi stack output application_url)
            echo "🔍 Verifying deployment at: $APP_URL"
            
            # Wait for the application to be accessible
            MAX_RETRIES=12
            RETRY_INTERVAL=15
            for i in $(seq 1 $MAX_RETRIES); do
              echo "Attempt $i/$MAX_RETRIES: Checking if application is accessible..."
              
              # Check if the application is responding (any HTTP response)
              if curl --silent --head --fail "$APP_URL" >/dev/null; then
                echo "✅ Application is accessible!"
                break
              fi
              
              if [ $i -eq $MAX_RETRIES ]; then
                echo "❌ Application failed to respond after $MAX_RETRIES attempts"
                exit 1
              fi
              
              echo "⏳ Application not yet accessible, waiting $RETRY_INTERVAL seconds..."
              sleep $RETRY_INTERVAL
            done
            
            # Check health endpoint
            echo "🩺 Checking health endpoint..."
            # Get health status code
            HEALTH_STATUS=$(curl -s -o /dev/null -w "%{http_code}" "$APP_URL/health")
            # Get health status content for detailed checks
            HEALTH_CONTENT=$(curl -s "$APP_URL/health")
            
            echo "Health check response: $HEALTH_CONTENT"
            
            if [ "$HEALTH_STATUS" == "200" ]; then
              echo "✅ Health check passed with status 200"
              
              # Parse JSON response to check detailed status
              if [[ "$HEALTH_CONTENT" == *"\"status\":\"degraded\""* ]]; then
                echo "⚠️ Health check reports degraded status"
                echo "Health check details: $HEALTH_CONTENT"
              else
                echo "✅ Service health is good"
              fi
            else
              echo "⚠️ Health check returned non-200 status: $HEALTH_STATUS"
              # Don't fail the build, but log the issue
            fi
            
            # Store the deployed URL as an artifact
            echo "$APP_URL" > ../deployment-url.txt
      
      # Run the version update script
      - run:
          name: Update Version Information
          command: |
            source $BASH_ENV
            
            # Run the version update script
            echo "📝 Updating version information..."
            chmod +x ./scripts/update-version.sh
            ./scripts/update-version.sh
            
            # Display the updated version information
            echo "Current version: $(cat version.txt)"
            echo "Detailed build info:"
            cat build-info.json
      
      # Upload deployment information as artifacts
      - store_artifacts:
          path: deployment-version.txt
          destination: deployment-version.txt
          
      - store_artifacts:
          path: deployment-url.txt
          destination: deployment-url.txt
          
      - store_artifacts:
          path: version.txt
          destination: version.txt
          
      - store_artifacts:
          path: build-info.json
          destination: build-info.json

workflows:
  build-test-deploy:
    jobs:
      - build-and-test

      - build-and-push-image: # Build and push Docker image after tests and linting pass
          requires:
            - build-and-test
          filters:
            branches:
              only: main

      # Deploy using Pulumi after tests pass
      - deploy-with-pulumi:
          requires:
            - build-and-test  # Directly depend on build-and-test
          filters:
            branches:
              only: main  # Only deploy from the main branch
