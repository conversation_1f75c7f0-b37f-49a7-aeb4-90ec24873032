# Trinote 2.0 Template Structure

This document outlines the template organization and patterns used in the Trinote 2.0 application.

## Directory Structure

```
app/templates/
├── base.html                # Base template with common structure
├── components/              # Reusable UI components
│   ├── forms/               # Form components
│   ├── modals/              # Modal dialogs
│   └── navigation/          # Navigation elements
├── layouts/                 # Page layout templates
│   ├── admin.html           # Admin layout
│   └── dashboard.html       # Dashboard layout
└── pages/                   # Page-specific templates
    ├── admin/               # Admin pages
    ├── auth/                # Authentication pages
    └── dashboard/           # Dashboard pages
```

## Template Inheritance Pattern

All page templates should inherit from a layout template:

```html
{% extends "layouts/dashboard.html" %}

{% block title %}Page Title{% endblock %}

{% block content %}
  <!-- Page-specific content here -->
{% endblock %}
```

## Component Usage Pattern

Reusable components should be included using the include directive:

```html
{% include "components/forms/user_form.html" %}
```

## Permission-Based UI Elements

UI elements that require specific permissions should be conditionally rendered:

```html
{% if permissions.get('view_reports') %}
  <a href="{{ url_for('routes.reports') }}" class="btn btn-primary">View Reports</a>
{% endif %}
```

## Flash Messages

Flash messages should be displayed in a consistent manner:

```html
{% with messages = get_flashed_messages(with_categories=true) %}
  {% if messages %}
    {% for category, message in messages %}
      <div class="alert alert-{{ category }}">
        {{ message }}
      </div>
    {% endfor %}
  {% endif %}
{% endwith %}
```

## Form Structure

Forms should follow this structure:

```html
<form method="POST" action="{{ url_for('routes.endpoint') }}">
  {{ form.csrf_token }}
  
  <div class="form-group">
    {{ form.field.label }}
    {{ form.field(class="form-control") }}
    {% if form.field.errors %}
      <div class="text-danger">
        {% for error in form.field.errors %}
          {{ error }}
        {% endfor %}
      </div>
    {% endif %}
  </div>
  
  <button type="submit" class="btn btn-primary">Submit</button>
</form>
```

## Navigation Structure

Navigation menus should be structured as follows:

```html
<nav class="navbar navbar-expand-lg navbar-light bg-light">
  <a class="navbar-brand" href="{{ url_for('routes.index') }}">Trinote</a>
  <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbarNav">
    <span class="navbar-toggler-icon"></span>
  </button>
  <div class="collapse navbar-collapse" id="navbarNav">
    <ul class="navbar-nav">
      {% if permissions.get('view_dashboard') %}
      <li class="nav-item {% if request.endpoint == 'routes.dashboard' %}active{% endif %}">
        <a class="nav-link" href="{{ url_for('routes.dashboard') }}">Dashboard</a>
      </li>
      {% endif %}
      
      {% if permissions.get('view_admin') %}
      <li class="nav-item {% if request.endpoint.startswith('admin.') %}active{% endif %}">
        <a class="nav-link" href="{{ url_for('admin.index') }}">Admin</a>
      </li>
      {% endif %}
    </ul>
  </div>
</nav>
```

## Data Tables

Data tables should be structured as follows:

```html
<table class="table table-striped">
  <thead>
    <tr>
      <th>ID</th>
      <th>Name</th>
      <th>Created</th>
      <th>Actions</th>
    </tr>
  </thead>
  <tbody>
    {% for item in items %}
    <tr>
      <td>{{ item.id }}</td>
      <td>{{ item.name }}</td>
      <td>{{ item.created_at.strftime('%Y-%m-%d') }}</td>
      <td>
        {% if permissions.get('edit_items') %}
        <a href="{{ url_for('routes.edit_item', id=item.id) }}" class="btn btn-sm btn-primary">Edit</a>
        {% endif %}
        
        {% if permissions.get('delete_items') %}
        <form method="POST" action="{{ url_for('routes.delete_item', id=item.id) }}" class="d-inline">
          <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
          <button type="submit" class="btn btn-sm btn-danger" onclick="return confirm('Are you sure?')">Delete</button>
        </form>
        {% endif %}
      </td>
    </tr>
    {% endfor %}
  </tbody>
</table>
```

## Pagination

Pagination should be implemented as follows:

```html
{% if pagination.pages > 1 %}
<nav>
  <ul class="pagination">
    {% if pagination.has_prev %}
    <li class="page-item">
      <a class="page-link" href="{{ url_for(request.endpoint, page=pagination.prev_num, **kwargs) }}">Previous</a>
    </li>
    {% else %}
    <li class="page-item disabled">
      <span class="page-link">Previous</span>
    </li>
    {% endif %}
    
    {% for page in pagination.iter_pages() %}
      {% if page %}
        {% if page != pagination.page %}
        <li class="page-item">
          <a class="page-link" href="{{ url_for(request.endpoint, page=page, **kwargs) }}">{{ page }}</a>
        </li>
        {% else %}
        <li class="page-item active">
          <span class="page-link">{{ page }}</span>
        </li>
        {% endif %}
      {% else %}
        <li class="page-item disabled">
          <span class="page-link">...</span>
        </li>
      {% endif %}
    {% endfor %}
    
    {% if pagination.has_next %}
    <li class="page-item">
      <a class="page-link" href="{{ url_for(request.endpoint, page=pagination.next_num, **kwargs) }}">Next</a>
    </li>
    {% else %}
    <li class="page-item disabled">
      <span class="page-link">Next</span>
    </li>
    {% endif %}
  </ul>
</nav>
{% endif %}
```
