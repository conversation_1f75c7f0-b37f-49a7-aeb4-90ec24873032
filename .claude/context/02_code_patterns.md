# Trinote 2.0 Code Patterns

This document outlines the standard code patterns used throughout the Trinote 2.0 project. Following these patterns ensures consistency and maintainability.

## Route Definition Pattern

Routes should include permission checks and proper error handling:

```python
@routes.route('/endpoint', methods=['GET', 'POST'])
@login_required
def endpoint_name():
    """
    Endpoint description.
    
    Returns:
        Rendered template or JSON response
    """
    # Permission check
    if not current_user.has_permission('required_permission'):
        flash('Permission denied', 'danger')
        return redirect(url_for('routes.index'))
    
    # Logic here
    
    return render_template('template.html', context=context_data)
```

## Model Definition Pattern

Models should include standard methods and proper documentation:

```python
class ModelName(db.Model):
    """
    Model description.
    """
    __tablename__ = 'model_names'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    related_models = db.relationship('RelatedModel', backref='model_name', lazy=True)
    
    def __repr__(self):
        return f'<ModelName {self.name}>'
    
    def to_dict(self):
        """Convert model to dictionary."""
        return {
            'id': self.id,
            'name': self.name,
            'created_at': self.created_at,
            'updated_at': self.updated_at
        }
```

## Blueprint Registration Pattern

Blueprints should be registered in a consistent way:

```python
def register_blueprint(app):
    from .views import blueprint_name
    app.register_blueprint(blueprint_name, url_prefix='/prefix')
```

## Permission Check Pattern

Permission checks should follow this pattern:

```python
def check_permission(permission_name):
    """Check if current user has the specified permission."""
    user_id = session.get('user_id')
    if not user_id:
        return False
    
    user = User.query.get(user_id)
    if not user:
        return False
    
    return user.has_permission(permission_name)
```

## Database Connection Pattern

Database connections should be obtained using these patterns:

```python
def get_db_connection(read_only=False):
    """Get database connection based on configuration."""
    if app.config.get('USE_POSTGRES_ONLY'):
        # Use SQLAlchemy session for PostgreSQL
        return db.session
    else:
        # For SQL Server operations
        from app.database import get_read_only_connection, get_write_connection
        return get_read_only_connection() if read_only else get_write_connection()
```

## Error Handling Pattern

Error handlers should follow this pattern:

```python
@app.errorhandler(404)
def page_not_found(e):
    """Handle 404 errors."""
    return render_template('errors/404.html'), 404

@app.errorhandler(500)
def server_error(e):
    """Handle 500 errors."""
    # Log the error
    app.logger.error(f"500 error: {str(e)}")
    return render_template('errors/500.html'), 500
```

## Testing Pattern

Tests should follow this pattern:

```python
def test_feature_name(client, auth):
    """Test description."""
    # Setup
    auth.login()
    
    # Action
    response = client.get('/endpoint')
    
    # Assertions
    assert response.status_code == 200
    assert b'Expected content' in response.data
```

## Environment-Specific Configuration Pattern

Environment-specific configuration should follow this pattern:

```python
# Development
if app.config['ENV'] == 'development':
    app.config['DEBUG'] = True
    app.config['SQLALCHEMY_ECHO'] = True

# Production
if app.config['ENV'] == 'production':
    app.config['DEBUG'] = False
    app.config['SQLALCHEMY_ECHO'] = False
    app.config['SESSION_COOKIE_SECURE'] = True
```

## Context Processor Pattern

Context processors should follow this pattern:

```python
@app.context_processor
def inject_permissions():
    """Make permissions available to all templates."""
    user_id = session.get('user_id')
    permissions = {}
    
    if user_id:
        user = User.query.get(user_id)
        if user:
            # Get all permissions for the user
            for permission in Permission.query.all():
                permissions[permission.name] = user.has_permission(permission.name)
    
    return dict(permissions=permissions)
```

## Logging Configuration Pattern

Logging should be configured using this pattern:

```python
def configure_logging(app):
    """Configure application logging."""
    handler = logging.FileHandler('logs/app.log')
    handler.setLevel(logging.INFO)
    
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    handler.setFormatter(formatter)
    
    app.logger.addHandler(handler)
    app.logger.setLevel(logging.INFO)
```

## Template Structure Pattern

Templates should follow this structure:

```html
{% extends "layouts/dashboard.html" %}

{% block title %}Page Title{% endblock %}

{% block content %}
  <!-- Page-specific content here -->
  
  {% if permissions.get('view_reports') %}
    <a href="{{ url_for('routes.reports') }}" class="btn btn-primary">View Reports</a>
  {% endif %}
  
  {% with messages = get_flashed_messages(with_categories=true) %}
    {% if messages %}
      {% for category, message in messages %}
        <div class="alert alert-{{ category }}">
          {{ message }}
        </div>
      {% endfor %}
    {% endif %}
  {% endwith %}
{% endblock %}
```

## Form Structure Pattern

Forms should follow this structure:

```html
<form method="POST" action="{{ url_for('routes.endpoint') }}">
  {{ form.csrf_token }}
  
  <div class="form-group">
    {{ form.field.label }}
    {{ form.field(class="form-control") }}
    {% if form.field.errors %}
      <div class="text-danger">
        {% for error in form.field.errors %}
          {{ error }}
        {% endfor %}
      </div>
    {% endif %}
  </div>
  
  <button type="submit" class="btn btn-primary">Submit</button>
</form>
```
