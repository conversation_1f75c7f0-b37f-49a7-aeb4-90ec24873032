# Trinote 2.0 Project Overview

## Project Description
Trinote 2.0 is a modern task management system for ABA (Applied Behavior Analysis) providers built with Flask. The application helps manage tasks, users, and permissions in a healthcare/therapy context.

## Architecture
- **Type**: MVC (Model-View-Controller)
- **Framework**: <PERSON>lask (Python)
- **Database**: PostgreSQL (primary), SQL Server (secondary)
- **ORM**: SQLAlchemy
- **Authentication**: Custom middleware with role-based access control
- **Frontend**: Jinja2 templates with Bootstrap

## Key Components
- **Models**: `app/models.py` - SQLAlchemy database models
- **Views**: `app/templates` - Jinja2 templates
- **Controllers**: `app/routes.py` - Flask route handlers
- **Blueprints**: `app/blueprints` - Modular Flask blueprints
- **Admin Interface**: `app/admin.py` - Administrative functionality
- **Authentication**: `app/auth.py` - User authentication
- **Database Access**: `app/database.py` - Database connection management
- **Middleware**: `app/middleware/` - Request processing middleware

## Project Structure
```
trinote2.0/
├── app/                    # Main application code
│   ├── __init__.py         # Application factory
│   ├── admin.py            # Admin interface
│   ├── auth.py             # Authentication
│   ├── blueprints/         # Modular blueprints
│   ├── database.py         # Database utilities
│   ├── db_connector.py     # Database connection
│   ├── frontend/           # Frontend assets
│   ├── middleware/         # Request middleware
│   ├── models.py           # Database models
│   ├── postgres_db.py      # PostgreSQL specific code
│   ├── routes.py           # Main route handlers
│   ├── static/             # Static assets
│   ├── templates/          # HTML templates
│   ├── test_routes.py      # Test endpoints
│   └── utils/              # Utility functions
├── config.py               # Application configuration
├── docker-compose.yml      # Development environment
├── Dockerfile              # Container definition
├── migrations/             # Database migrations
├── requirements.txt        # Python dependencies
├── run.py                  # Application entry point
├── scripts/                # Utility scripts
├── sql/                    # SQL scripts
├── tasks/                  # Background tasks
└── tests/                  # Test suite
```

## Technology Stack
- **Python**: 3.8+
- **Flask**: Web framework
- **SQLAlchemy**: ORM for database access
- **PostgreSQL**: Primary database
- **SQL Server**: Secondary database (legacy/integration)
- **Docker**: Containerization
- **Pulumi**: Infrastructure as Code
- **HashiCorp Vault**: Secrets management

## Development Environment
- **Docker Compose**: Multi-container setup with PostgreSQL, SQL Server, and application
- **Virtual Environment**: Python venv for local development
- **Environment Variables**: Configuration via .env files
- **Testing**: pytest for unit and integration tests

## Deployment
- **Environments**: Development, Staging, Production
- **Infrastructure**: Cloud-based deployment managed with Pulumi
- **CI/CD**: Automated testing and deployment pipeline
- **Monitoring**: Health check endpoints for load balancer integration

## Conventions
- **Python Version**: 3.8+
- **Indentation**: 4 spaces
- **Line Length**: 100 characters
- **Docstring Style**: Google
- **Quoting Style**: Single quotes for strings, double quotes for docstrings
- **Naming**:
  - Variables/Functions: snake_case
  - Classes: PascalCase
  - Constants: UPPER_SNAKE_CASE
  - Files: snake_case.py
  - Tables: snake_case_plural
