# Trinote 2.0 Security

This document outlines the security architecture and best practices for the Trinote 2.0 application.

## Authentication System

Trinote 2.0 uses a custom authentication middleware located in `app/middleware/auth.py`. The authentication system includes:

- Session-based authentication
- Role-based access control
- Permission-based authorization
- Secure password handling

### Authentication Flow

1. User submits credentials via login form
2. Credentials are validated against the database
3. On successful authentication:
   - User ID is stored in session
   - User role and permissions are loaded
   - Last login timestamp is updated
4. Session is secured with HTTP-only cookies

## Authorization Model

The authorization model is based on a role-based access control (RBAC) system with user-specific permission overrides:

- **Roles**: Define sets of permissions (e.g., Admin, Manager, User)
- **Permissions**: Granular access controls for specific actions
- **User Permissions**: Override role-based permissions for specific users
- **Route Permissions**: Map permissions to specific application routes

### Permission Check Implementation

```python
def has_permission(user, permission_name):
    """
    Check if a user has a specific permission.
    
    Args:
        user: User object
        permission_name: Name of the permission to check
        
    Returns:
        bool: True if user has permission, False otherwise
    """
    # Check user-specific permissions first (these override role permissions)
    for user_perm in user.permissions:
        if user_perm.permission.name == permission_name:
            return user_perm.granted
    
    # If no user-specific permission, check role permissions
    if user.role:
        for role_perm in user.role.role_permissions:
            if role_perm.permission.name == permission_name:
                return True
    
    return False
```

## Secrets Management

Trinote 2.0 uses HashiCorp Vault for secrets management:

- Database credentials
- API keys
- Encryption keys
- Environment-specific secrets

### Vault Integration

```python
def get_secret(path, key):
    """
    Get a secret from HashiCorp Vault.
    
    Args:
        path: Path to the secret in Vault
        key: Key of the secret
        
    Returns:
        str: Secret value
    """
    import hvac
    import os
    
    # Connect to Vault
    client = hvac.Client(
        url=os.environ.get('VAULT_ADDR', 'http://127.0.0.1:8200'),
        token=os.environ.get('VAULT_TOKEN')
    )
    
    # Get secret
    secret = client.secrets.kv.v2.read_secret_version(path=path)
    
    # Return specific key
    return secret['data']['data'].get(key)
```

## Data Protection

### Database Security

- Parameterized queries to prevent SQL injection
- Encrypted connections to databases
- Least privilege database users
- Regular security audits

### Input Validation

All user input is validated:

```python
def validate_input(data, schema):
    """
    Validate user input against a schema.
    
    Args:
        data: User input data
        schema: Validation schema
        
    Returns:
        tuple: (is_valid, errors)
    """
    from marshmallow import ValidationError
    
    try:
        validated_data = schema.load(data)
        return True, validated_data
    except ValidationError as e:
        return False, e.messages
```

### Output Encoding

All output is properly encoded to prevent XSS:

```python
def safe_render(template, **context):
    """
    Safely render a template with context.
    
    Args:
        template: Template name
        context: Template context
        
    Returns:
        str: Rendered template
    """
    from flask import render_template
    from markupsafe import escape
    
    # Escape any user-provided content
    for key, value in context.items():
        if isinstance(value, str):
            context[key] = escape(value)
    
    return render_template(template, **context)
```

## Security Headers

The application sets the following security headers:

```python
@app.after_request
def set_security_headers(response):
    """Set security headers on all responses."""
    response.headers['X-Content-Type-Options'] = 'nosniff'
    response.headers['X-Frame-Options'] = 'SAMEORIGIN'
    response.headers['X-XSS-Protection'] = '1; mode=block'
    response.headers['Content-Security-Policy'] = "default-src 'self'"
    response.headers['Strict-Transport-Security'] = 'max-age=31536000; includeSubDomains'
    return response
```

## Session Security

Sessions are secured with the following measures:

```python
app.config.update(
    SESSION_COOKIE_SECURE=True,
    SESSION_COOKIE_HTTPONLY=True,
    SESSION_COOKIE_SAMESITE='Lax',
    PERMANENT_SESSION_LIFETIME=timedelta(hours=1)
)
```

## Security Best Practices

1. **Regular Updates**: Keep all dependencies updated
2. **Security Scanning**: Regular vulnerability scanning
3. **Audit Logging**: Log all security-relevant events
4. **Credential Rotation**: Regularly rotate credentials
5. **Least Privilege**: Follow principle of least privilege
6. **Defense in Depth**: Multiple layers of security controls
7. **Secure Defaults**: Security by default, not by configuration
8. **Error Handling**: Secure error handling that doesn't leak information
