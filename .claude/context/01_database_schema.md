# Trinote 2.0 Database Schema

## Database Architecture
Trinote 2.0 uses a dual-database approach:
- **PostgreSQL**: Primary database for application data, user management, and permissions
- **SQL Server**: Secondary database for legacy data or integration with existing systems

## SQLAlchemy Models

### User Model
```python
class User(db.Model):
    """
    Represents users from the external SQL database.
    This table stores user mappings and additional attributes.
    """
    __tablename__ = 'users'

    id = db.Column(db.Integer, primary_key=True)
    external_id = db.Column(db.Integer, nullable=False, unique=True)
    username = db.Column(db.String(100), nullable=False)
    email = db.Column(db.String(100), nullable=True)
    role_id = db.Column(db.Integer, db.ForeignKey('roles.id'))
    is_active = db.Column(db.Bo<PERSON>an, default=True)
    last_login = db.Column(db.DateTime, nullable=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # RBAC Relationships
    permissions = db.relationship(
        'UserPermission', back_populates='user', cascade='all, delete-orphan'
    )
```

### Role Model
```python
class Role(db.Model):
    """
    Represents user roles in the system.
    Each role contains a set of permissions.
    """
    __tablename__ = 'roles'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(80), unique=True, nullable=False)
    description = db.Column(db.String(255))
    
    # Relationships
    users = db.relationship('User', backref='role', lazy=True)
    role_permissions = db.relationship(
        'RolePermission', back_populates='role', cascade='all, delete-orphan'
    )
```

### Permission Model
```python
class Permission(db.Model):
    """
    Represents system permissions that can be assigned to roles or users.
    """
    __tablename__ = 'permissions'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False, unique=True)
    description = db.Column(db.String(255), nullable=True)
    category = db.Column(db.String(50), nullable=True)  # For grouping permissions
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # Relationships
    role_permissions = db.relationship(
        'RolePermission', back_populates='permission', cascade='all, delete-orphan'
    )
    user_permissions = db.relationship(
        'UserPermission', back_populates='permission', cascade='all, delete-orphan'
    )
```

### RolePermission Model
```python
class RolePermission(db.Model):
    """
    Associates permissions with roles (many-to-many relationship).
    """
    __tablename__ = 'role_permissions'
    
    role_id = db.Column(db.Integer, db.ForeignKey('roles.id'), primary_key=True)
    permission_id = db.Column(db.Integer, db.ForeignKey('permissions.id'), primary_key=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # Relationships
    role = db.relationship('Role', back_populates='role_permissions')
    permission = db.relationship('Permission', back_populates='role_permissions')
```

### UserPermission Model
```python
class UserPermission(db.Model):
    """
    Associates permissions directly with users (many-to-many relationship).
    This allows for individual user permission overrides.
    """
    __tablename__ = 'user_permissions'

    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), primary_key=True)
    permission_id = db.Column(db.Integer, db.ForeignKey('permissions.id'), primary_key=True)
    granted = db.Column(db.Boolean, default=True)  # True=granted, False=explicitly denied
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    user = db.relationship('User', back_populates='permissions')
    permission = db.relationship('Permission', back_populates='user_permissions')
```

### RoutePermission Model
```python
class RoutePermission(db.Model):
    """
    Associates permissions with routes for persistent route-based permission checks.
    """
    __tablename__ = 'route_permissions'
    
    id = db.Column(db.Integer, primary_key=True)
    endpoint = db.Column(db.String(255), nullable=False, index=True)
    permission_id = db.Column(db.Integer, db.ForeignKey('permissions.id', ondelete='CASCADE'), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # Relationships
    permission = db.relationship('Permission', backref=db.backref('route_permissions', cascade='all, delete-orphan'))
```

### Message Model
```python
class Message(db.Model):
    __tablename__ = 'messages'

    id = db.Column(db.Integer, primary_key=True)
    sender_name = db.Column(db.String(100), nullable=False)
    sender_id = db.Column(db.Integer)
    recipient_id = db.Column(db.Integer)
    recipient_name = db.Column(db.String(100), nullable=True)
    subject = db.Column(db.String(255), nullable=False, default="(No subject)")
    content = db.Column(db.Text, nullable=False)
    timestamp = db.Column(db.DateTime, default=datetime.utcnow)
    read = db.Column(db.Boolean, default=False)
    folder = db.Column(db.String(20), default="inbox")  # inbox, sent, drafts, trash, junk
    is_starred = db.Column(db.Boolean, default=False)
    label = db.Column(db.String(20), nullable=True)  # important, social, promotions, etc.
```

## Database Relationships

### User Relationships
- A User belongs to one Role
- A User has many UserPermissions (direct permission assignments)

### Role Relationships
- A Role has many Users
- A Role has many RolePermissions

### Permission Relationships
- A Permission has many RolePermissions
- A Permission has many UserPermissions
- A Permission has many RoutePermissions

## Database Access Patterns

### SQLAlchemy Session
For PostgreSQL database access, use the SQLAlchemy session:

```python
from app.models import db, User

# Query example
user = User.query.filter_by(username='example').first()

# Create example
new_user = User(username='newuser', email='<EMAIL>')
db.session.add(new_user)
db.session.commit()
```

### SQL Server Connection
For SQL Server access, use the connection utilities:

```python
from app.database import get_read_only_connection, get_write_connection

# Read-only operations
conn = get_read_only_connection()
cursor = conn.cursor()
cursor.execute('SELECT * FROM some_table')
results = cursor.fetchall()
cursor.close()
conn.close()

# Write operations
conn = get_write_connection()
cursor = conn.cursor()
cursor.execute('INSERT INTO some_table (column) VALUES (?)', ['value'])
conn.commit()
cursor.close()
conn.close()
```

## Migration Management
Database migrations are managed with Flask-Migrate:

```bash
# Create a new migration
flask db migrate -m "Description of changes"

# Apply migrations
flask db upgrade

# Rollback migration
flask db downgrade
```
