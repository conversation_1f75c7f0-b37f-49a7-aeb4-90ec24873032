# Trinote 2.0 Workflows

This document outlines the standard workflows for development, testing, and deployment in the Trinote 2.0 project.

## Development Workflow

### Environment Setup

```bash
# Clone the repository
git clone <repository-url>
cd trinote2.0

# Create and activate virtual environment
python -m venv .venv
source .venv/bin/activate  # On Windows: .venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt

# Set up environment variables
cp .env.example .env
# Edit .env with your configuration

# Start development environment with Docker
docker-compose up -d
```

### Local Development

```bash
# Run the application
python run.py

# Run with Flask development server
export FLASK_APP=run.py
export FLASK_ENV=development
flask run
```

### Docker Development

```bash
# Start all services
docker-compose up -d

# View logs
docker-compose logs -f app

# Run tests in container
docker-compose exec app pytest

# Apply database migrations
docker-compose exec app flask db upgrade
```

## Database Migration Workflow

```bash
# Create a new migration
flask db migrate -m "Description of changes"

# Apply migrations
flask db upgrade

# Rollback migration
flask db downgrade
```

## Feature Development Workflow

1. **Create a new branch**
   ```bash
   git checkout -b feature/feature-name
   ```

2. **Implement the feature**
   - Follow the code patterns in the project
   - Add appropriate tests in `tests/`

3. **Run tests locally**
   ```bash
   pytest
   ```

4. **Submit a pull request**
   - Include a description of the changes
   - Reference any related issues
   - Ensure CI checks pass

5. **Code review**
   - Address feedback
   - Update tests if necessary

6. **Merge to main branch**
   - Squash commits if necessary
   - Delete feature branch after merging

## Bug Fix Workflow

1. **Create a bug fix branch**
   ```bash
   git checkout -b fix/bug-description
   ```

2. **Reproduce the bug**
   - Write a failing test that demonstrates the bug

3. **Fix the bug**
   - Implement the fix
   - Ensure the test now passes

4. **Submit a pull request**
   - Include a description of the bug and fix
   - Reference the issue number

## Database Schema Change Workflow

1. **Plan the schema change**
   - Document the changes in a design document
   - Consider backward compatibility

2. **Create a migration branch**
   ```bash
   git checkout -b migration/description
   ```

3. **Update models**
   - Modify the SQLAlchemy models in `app/models.py`

4. **Generate migration**
   ```bash
   flask db migrate -m "Description of schema changes"
   ```

5. **Review migration**
   - Check the generated migration script
   - Make any necessary adjustments

6. **Test migration**
   ```bash
   flask db upgrade
   flask db downgrade  # Test rollback
   flask db upgrade    # Apply again
   ```

7. **Update application code**
   - Update any code that interacts with the changed schema

8. **Submit a pull request**
   - Include migration details
   - Note any backward compatibility issues

## Deployment Workflow

```bash
# 1. Run all tests
pytest

# 2. Check database migrations
flask db check

# 3. Update version information
echo "$(git rev-parse HEAD)" > version.txt

# 4. Build Docker image
docker build -t trinote:latest .

# 5. Push to registry
docker tag trinote:latest registry.example.com/trinote:latest
docker push registry.example.com/trinote:latest

# 6. Deploy with Pulumi
cd pulumi
pulumi up --stack dev  # or stage, prod

# 7. Verify health checks
curl https://[domain]/health

# 8. Monitor logs for errors
```

## Deployment Checklist

### Pre-Deployment Checks

- [ ] All tests pass (`pytest`)
- [ ] Database migrations are up to date (`flask db check`)
- [ ] Environment variables are configured correctly
- [ ] Static assets are compiled and optimized
- [ ] Documentation is updated
- [ ] Version information is updated
- [ ] Security vulnerabilities are addressed
- [ ] Performance benchmarks meet requirements

### Deployment Steps

1. **Prepare the Release**
   - [ ] Tag the release in Git
   - [ ] Update version information
   - [ ] Generate release notes

2. **Build the Application**
   - [ ] Build Docker image
   - [ ] Run tests in the container
   - [ ] Push image to registry

3. **Deploy to Target Environment**
   - [ ] Run Pulumi deployment
   - [ ] Verify infrastructure changes
   - [ ] Apply database migrations

4. **Verify Deployment**
   - [ ] Check health endpoint (`/health`)
   - [ ] Verify version endpoint (`/version`)
   - [ ] Test critical user flows
   - [ ] Monitor application logs
   - [ ] Check database performance

5. **Post-Deployment Tasks**
   - [ ] Update monitoring alerts
   - [ ] Notify stakeholders
   - [ ] Update documentation
   - [ ] Schedule post-deployment review

### Rollback Procedure

If deployment fails or critical issues are found:

1. **Assess the Issue**
   - [ ] Identify the root cause
   - [ ] Determine impact on users
   - [ ] Decide if rollback is necessary

2. **Rollback Steps**
   - [ ] Revert to previous Pulumi stack
   - [ ] Rollback database migrations if necessary
   - [ ] Verify system functionality after rollback

3. **Communication**
   - [ ] Notify stakeholders of rollback
   - [ ] Document the issue and rollback process
   - [ ] Schedule fix for next deployment
