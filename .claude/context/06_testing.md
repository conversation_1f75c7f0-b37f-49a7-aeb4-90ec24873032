# Trinote 2.0 Testing Guidelines

This document outlines the testing approach and patterns used in the Trinote 2.0 application.

## Testing Structure

```
tests/
├── conftest.py             # Test fixtures and configuration
├── unit/                   # Unit tests
│   ├── test_models.py      # Tests for database models
│   └── test_utils.py       # Tests for utility functions
├── integration/            # Integration tests
│   ├── test_routes.py      # Tests for route handlers
│   └── test_auth.py        # Tests for authentication
└── functional/             # Functional/E2E tests
    └── test_workflows.py   # Tests for complete workflows
```

## Test Fixtures

Standard fixtures are defined in `conftest.py`:

```python
import pytest
from app import create_app
from app.models import db, User, Role

@pytest.fixture
def app():
    """Create and configure a Flask app for testing."""
    app = create_app('testing')
    
    # Create tables
    with app.app_context():
        db.create_all()
    
    yield app
    
    # Clean up
    with app.app_context():
        db.drop_all()

@pytest.fixture
def client(app):
    """A test client for the app."""
    return app.test_client()

@pytest.fixture
def runner(app):
    """A test CLI runner for the app."""
    return app.test_cli_runner()

@pytest.fixture
def auth(client):
    """Authentication helper for tests."""
    class AuthActions:
        def __init__(self, client):
            self._client = client
            
        def login(self, username='testuser', password='password'):
            return self._client.post(
                '/auth/login',
                data={'username': username, 'password': password},
                follow_redirects=True
            )
            
        def logout(self):
            return self._client.get('/auth/logout', follow_redirects=True)
    
    return AuthActions(client)

@pytest.fixture
def user(app):
    """Create a test user."""
    with app.app_context():
        role = Role.query.filter_by(name='User').first()
        if not role:
            role = Role(name='User', description='Regular user')
            db.session.add(role)
            
        user = User(
            username='testuser',
            email='<EMAIL>',
            role=role,
            is_active=True
        )
        db.session.add(user)
        db.session.commit()
        
        return user
```

## Unit Test Pattern

Unit tests should follow this pattern:

```python
def test_user_model(app):
    """Test User model."""
    with app.app_context():
        # Setup
        user = User(username='testuser', email='<EMAIL>')
        db.session.add(user)
        db.session.commit()
        
        # Test
        retrieved_user = User.query.filter_by(username='testuser').first()
        
        # Assert
        assert retrieved_user is not None
        assert retrieved_user.username == 'testuser'
        assert retrieved_user.email == '<EMAIL>'
```

## Route Test Pattern

Route tests should follow this pattern:

```python
def test_home_page(client):
    """Test home page route."""
    # Setup
    
    # Action
    response = client.get('/')
    
    # Assert
    assert response.status_code == 200
    assert b'Welcome to Trinote' in response.data
```

## Authentication Test Pattern

Authentication tests should follow this pattern:

```python
def test_login(client, auth):
    """Test login functionality."""
    # Setup
    
    # Action
    response = auth.login()
    
    # Assert
    assert response.status_code == 200
    with client.session_transaction() as session:
        assert session['user_id'] is not None
```

## Permission Test Pattern

Permission tests should follow this pattern:

```python
def test_permission_required(client, auth, user, app):
    """Test that a route requires specific permissions."""
    # Setup
    auth.login()
    
    # Action - try to access a protected route
    response = client.get('/admin/users')
    
    # Assert - should be redirected because user doesn't have permission
    assert response.status_code == 302
    
    # Now give the user the required permission
    with app.app_context():
        from app.models import Permission, UserPermission
        perm = Permission.query.filter_by(name='view_users').first()
        if not perm:
            perm = Permission(name='view_users', description='Can view users')
            db.session.add(perm)
            db.session.commit()
        
        user_perm = UserPermission(user_id=user.id, permission_id=perm.id, granted=True)
        db.session.add(user_perm)
        db.session.commit()
    
    # Try again
    response = client.get('/admin/users')
    
    # Should now have access
    assert response.status_code == 200
```

## Database Test Pattern

Database tests should follow this pattern:

```python
def test_database_operation(app):
    """Test database operations."""
    with app.app_context():
        # Setup
        user = User(username='testuser', email='<EMAIL>')
        db.session.add(user)
        db.session.commit()
        
        # Action
        user.email = '<EMAIL>'
        db.session.commit()
        
        # Assert
        updated_user = User.query.get(user.id)
        assert updated_user.email == '<EMAIL>'
```

## API Test Pattern

API tests should follow this pattern:

```python
def test_api_endpoint(client, auth):
    """Test API endpoint."""
    # Setup
    auth.login()
    
    # Action
    response = client.get('/api/data')
    
    # Assert
    assert response.status_code == 200
    json_data = response.get_json()
    assert 'items' in json_data
    assert len(json_data['items']) > 0
```

## Running Tests

Tests can be run using pytest:

```bash
# Run all tests
pytest

# Run specific test file
pytest tests/unit/test_models.py

# Run specific test
pytest tests/unit/test_models.py::test_user_model

# Run with coverage
pytest --cov=app tests/
```
