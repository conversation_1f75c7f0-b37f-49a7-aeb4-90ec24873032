# Claude Context for Trinote 2.0

This directory contains configuration files and context information for Claude Code agent to provide better assistance with the Trinote 2.0 project.

## Contents

- `config.json`: Main configuration file that describes the project and context structure
- `context/`: Directory containing project context in Markdown format
  - `00_project_overview.md`: Project structure, architecture, and conventions
  - `01_database_schema.md`: Database models and relationships
  - `02_code_patterns.md`: Common code patterns used in the project
  - `03_workflows.md`: Development, testing, and deployment workflows
  - `04_security.md`: Security architecture and best practices
  - `05_template_structure.md`: Template organization and patterns
  - `06_testing.md`: Testing approach and patterns
- `update_context.py`: <PERSON>ript to update Claude context from Amazon Q profile

## Usage

When using Claude Code agent, you can reference this context to get more relevant assistance:

1. **API Usage**: Include these context files in your API requests
2. **Web Interface**: Upload or reference these files at the start of your conversation
3. **Claude CLI**: Use the `--context` flag to include these files

Example:
```bash
claude chat --context .claude/context/
```

## Updating the Context

As the project evolves, you can update the context files manually or use the provided script:

```bash
python .claude/update_context.py
```

This script will extract information from the Amazon Q project profile and update the Claude context files.

## Benefits

- More accurate code suggestions
- Better understanding of project structure
- Consistent code generation following project conventions
- Improved assistance with database queries and models
