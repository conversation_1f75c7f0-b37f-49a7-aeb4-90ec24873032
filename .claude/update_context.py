#!/usr/bin/env python3
"""
Update Claude Context

This script extracts information from the Amazon Q project profile
and updates the Claude context files.
"""

import os
import json
import sys
from pathlib import Path

def ensure_directory(path):
    """Ensure directory exists."""
    os.makedirs(path, exist_ok=True)

def read_json_file(path):
    """Read JSON file."""
    try:
        with open(path, 'r') as f:
            return json.load(f)
    except Exception as e:
        print(f"Error reading {path}: {e}")
        return {}

def write_markdown_file(path, content):
    """Write markdown file."""
    try:
        with open(path, 'w') as f:
            f.write(content)
        print(f"Updated {path}")
    except Exception as e:
        print(f"Error writing {path}: {e}")

def update_project_overview(q_profile, claude_context_dir):
    """Update project overview from Q profile."""
    overview_content = f"""# {q_profile.get('name', 'Project')} Overview

## Project Description
{q_profile.get('description', 'No description available.')}

## Architecture
- **Type**: {q_profile.get('architecture', {}).get('type', 'Unknown')}
"""

    # Add frameworks
    if 'frameworks' in q_profile:
        overview_content += "\n## Frameworks\n"
        for framework in q_profile.get('frameworks', []):
            overview_content += f"- **{framework.get('name')}**: {framework.get('version', '')}\n"

    # Add languages
    if 'languages' in q_profile:
        overview_content += "\n## Languages\n"
        for language in q_profile.get('languages', []):
            overview_content += f"- **{language.get('name')}**: {language.get('version', '')}\n"

    # Add project structure
    if 'projectStructure' in q_profile:
        overview_content += "\n## Project Structure\n"
        structure = q_profile.get('projectStructure', {})
        
        if 'rootDirectory' in structure:
            overview_content += f"- **Root Directory**: {structure.get('rootDirectory')}\n"
        
        if 'sourceDirectories' in structure:
            overview_content += "- **Source Directories**:\n"
            for dir in structure.get('sourceDirectories', []):
                overview_content += f"  - {dir}\n"
        
        if 'testDirectories' in structure:
            overview_content += "- **Test Directories**:\n"
            for dir in structure.get('testDirectories', []):
                overview_content += f"  - {dir}\n"
        
        if 'configurationFiles' in structure:
            overview_content += "- **Configuration Files**:\n"
            for file in structure.get('configurationFiles', []):
                overview_content += f"  - {file}\n"

    # Add conventions
    if 'conventions' in q_profile:
        overview_content += "\n## Conventions\n"
        conventions = q_profile.get('conventions', {})
        
        if 'naming' in conventions:
            overview_content += "- **Naming Conventions**:\n"
            naming = conventions.get('naming', {})
            for key, value in naming.items():
                overview_content += f"  - {key}: {value}\n"
        
        if 'formatting' in conventions:
            overview_content += "- **Formatting Conventions**:\n"
            formatting = conventions.get('formatting', {})
            for key, value in formatting.items():
                overview_content += f"  - {key}: {value}\n"
        
        if 'documentation' in conventions:
            overview_content += "- **Documentation Conventions**:\n"
            documentation = conventions.get('documentation', {})
            for key, value in documentation.items():
                overview_content += f"  - {key}: {value}\n"

    # Write to file
    write_markdown_file(os.path.join(claude_context_dir, '00_project_overview.md'), overview_content)

def update_code_patterns(q_profile, claude_context_dir):
    """Update code patterns from Q profile."""
    patterns_content = f"""# {q_profile.get('name', 'Project')} Code Patterns

This document outlines the standard code patterns used throughout the project.

"""

    # Add code patterns
    if 'codePatterns' in q_profile:
        for name, pattern in q_profile.get('codePatterns', {}).items():
            patterns_content += f"## {name.replace('_', ' ').title()}\n\n"
            patterns_content += f"{pattern.get('description', '')}\n\n"
            patterns_content += "```python\n"
            patterns_content += f"{pattern.get('pattern', '')}\n"
            patterns_content += "```\n\n"

    # Write to file
    write_markdown_file(os.path.join(claude_context_dir, '02_code_patterns.md'), patterns_content)

def update_workflows(q_profile, claude_context_dir):
    """Update workflows from Q profile."""
    workflows_content = f"""# {q_profile.get('name', 'Project')} Workflows

This document outlines the standard workflows for development, testing, and deployment.

"""

    # Add workflows
    if 'workflows' in q_profile:
        workflows = q_profile.get('workflows', {})
        
        if 'development' in workflows:
            workflows_content += "## Development Workflow\n\n"
            dev = workflows.get('development', {})
            
            if 'setup' in dev:
                workflows_content += "### Setup\n\n```bash\n"
                workflows_content += f"{dev.get('setup')}\n"
                workflows_content += "```\n\n"
            
            if 'testing' in dev:
                workflows_content += "### Testing\n\n```bash\n"
                workflows_content += f"{dev.get('testing')}\n"
                workflows_content += "```\n\n"
            
            if 'linting' in dev:
                workflows_content += "### Linting\n\n```bash\n"
                workflows_content += f"{dev.get('linting')}\n"
                workflows_content += "```\n\n"
        
        if 'database' in workflows:
            workflows_content += "## Database Workflow\n\n"
            db = workflows.get('database', {})
            
            if 'migrations' in db:
                workflows_content += "### Migrations\n\n```bash\n"
                migrations = db.get('migrations', {})
                for key, value in migrations.items():
                    workflows_content += f"# {key.replace('_', ' ').title()}\n"
                    workflows_content += f"{value}\n\n"
                workflows_content += "```\n\n"
        
        if 'deployment' in workflows:
            workflows_content += "## Deployment Workflow\n\n"
            deploy = workflows.get('deployment', {})
            
            if 'environments' in deploy:
                workflows_content += "### Environments\n\n"
                for env in deploy.get('environments', []):
                    workflows_content += f"- {env}\n"
                workflows_content += "\n"
            
            if 'commands' in deploy:
                workflows_content += "### Commands\n\n```bash\n"
                commands = deploy.get('commands', {})
                for key, value in commands.items():
                    workflows_content += f"# {key.replace('_', ' ').title()}\n"
                    workflows_content += f"{value}\n\n"
                workflows_content += "```\n\n"

    # Write to file
    write_markdown_file(os.path.join(claude_context_dir, '03_workflows.md'), workflows_content)

def update_security(q_profile, claude_context_dir):
    """Update security information from Q profile."""
    security_content = f"""# {q_profile.get('name', 'Project')} Security

This document outlines the security architecture and best practices.

"""

    # Add security information
    if 'security' in q_profile:
        security = q_profile.get('security', {})
        
        security_content += "## Authentication\n\n"
        security_content += f"{security.get('authentication', 'Not specified')}\n\n"
        
        security_content += "## Authorization\n\n"
        security_content += f"{security.get('authorization', 'Not specified')}\n\n"
        
        security_content += "## Secrets Management\n\n"
        security_content += f"{security.get('secretsManagement', 'Not specified')}\n\n"

    # Write to file
    write_markdown_file(os.path.join(claude_context_dir, '04_security.md'), security_content)

def main():
    """Main function."""
    # Get project root
    project_root = Path(__file__).parent.parent
    
    # Define paths
    q_profile_path = project_root / '.amazon-q' / 'project-profile.json'
    claude_context_dir = project_root / '.claude' / 'context'
    
    # Ensure directories exist
    ensure_directory(claude_context_dir)
    
    # Read Amazon Q profile
    q_profile = read_json_file(q_profile_path)
    if not q_profile:
        print("Error: Could not read Amazon Q profile.")
        sys.exit(1)
    
    # Update Claude context files
    update_project_overview(q_profile, claude_context_dir)
    update_code_patterns(q_profile, claude_context_dir)
    update_workflows(q_profile, claude_context_dir)
    update_security(q_profile, claude_context_dir)
    
    print("Claude context files updated successfully.")

if __name__ == "__main__":
    main()
